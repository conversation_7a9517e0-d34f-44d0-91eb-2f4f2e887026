// 网络服务
// 获取网络信息和连接状态

import connection from '@ohos.net.connection';
import http from '@ohos.net.http';
import { NetworkInfo, TestServer, NetworkType } from '../common/types/SpeedTestModels';
import { TypeConverter } from '../common/utils/TypeConverter';

/**
 * IP响应接口
 */
interface IpResponse {
  ip?: string;
}

/**
 * 位置信息接口
 */
interface LocationInfo {
  country: string;
  city: string;
  isp: string;
}

/**
 * 网络质量评估指标接口（借鉴LibreSpeed的质量评估体系）
 */
interface NetworkQualityMetrics {
  bandwidth: number;      // 带宽 (Mbps)
  latency: number;        // 延迟 (ms)
  jitter: number;         // 抖动 (ms)
  packetLoss: number;     // 丢包率 (%)
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  score: number;          // 综合评分 (0-100)
}

/**
 * 位置数据接口
 */
interface LocationData {
  country_name?: string;
  city?: string;
  org?: string;
}

/**
 * 网络服务类
 */
export class NetworkService {
  private readonly IP_API_URL = 'https://api.ipify.org?format=json';
  private readonly GEO_API_URL = 'https://ipapi.co/json/';
  
  /**
   * 获取网络连接信息
   */
  async getNetworkInfo(): Promise<NetworkInfo> {
    try {
      // 获取网络连接状态
      const netHandle = await connection.getDefaultNet();
      const netCapabilities = await connection.getNetCapabilities(netHandle);
      const connectionProperties = await connection.getConnectionProperties(netHandle);
      
      // 获取IP地址
      const ipAddress = await this.getPublicIP();
      
      // 判断网络类型（增强检测逻辑）
      let networkType = NetworkType.UNKNOWN;
      if (netCapabilities.bearerTypes && netCapabilities.bearerTypes.includes(connection.NetBearType.BEARER_WIFI)) {
        networkType = await this.detectWifiType();
      } else if (netCapabilities.bearerTypes && netCapabilities.bearerTypes.includes(connection.NetBearType.BEARER_CELLULAR)) {
        networkType = await this.detectCellularType();
      } else if (netCapabilities.bearerTypes && netCapabilities.bearerTypes.includes(connection.NetBearType.BEARER_ETHERNET)) {
        networkType = await this.detectEthernetType();
      }
      
      return {
        type: networkType,
        operator: 'Unknown', // 需要额外API获取运营商信息
        ipAddress: ipAddress,
        signalStrength: 0, // 需要额外API获取信号强度
        isConnected: netHandle !== null
      };
    } catch (error) {
      console.error('获取网络信息失败:', error);
      // 返回默认网络信息
      return {
        type: NetworkType.UNKNOWN,
        operator: 'Unknown',
        ipAddress: '0.0.0.0',
        signalStrength: 0,
        isConnected: false
      };
    }
  }
  
  /**
   * 检查网络连接状态
   */
  async checkConnectivity(): Promise<boolean> {
    try {
      const netHandle = await connection.getDefaultNet();
      return netHandle !== null;
    } catch (error) {
      console.error('检查网络连接失败:', error);
      return false;
    }
  }
  
  /**
   * 获取公网IP地址
   */
  async getPublicIP(): Promise<string> {
    try {
      const httpRequest = http.createHttp();
      const response = await httpRequest.request(this.IP_API_URL, {
        method: http.RequestMethod.GET,
        connectTimeout: 10000,
        readTimeout: 10000
      });
      
      httpRequest.destroy();
      
      if (response.result && typeof response.result === 'string') {
        const data: IpResponse = JSON.parse(response.result);
        return data.ip || '0.0.0.0';
      }
      
      return '0.0.0.0';
    } catch (error) {
      console.error('获取公网IP失败:', error);
      return '0.0.0.0';
    }
  }
  
  /**
   * 获取地理位置信息
   */
  async getLocationInfo(): Promise<LocationInfo> {
    try {
      const httpRequest = http.createHttp();
      const response = await httpRequest.request(this.GEO_API_URL, {
        method: http.RequestMethod.GET,
        connectTimeout: 10000,
        readTimeout: 10000
      });
      
      httpRequest.destroy();
      
      if (response.result && typeof response.result === 'string') {
        const data = JSON.parse(response.result as string) as LocationData;
        const locationInfo: LocationInfo = {
        country: data.country_name || 'Unknown',
        city: data.city || 'Unknown',
        isp: data.org || 'Unknown'
      };
      return locationInfo;
      }
      
      const defaultLocationInfo: LocationInfo = {
      country: 'Unknown',
      city: 'Unknown',
      isp: 'Unknown'
    };
    return defaultLocationInfo;
    } catch (error) {
      console.error('获取地理位置信息失败:', error);
      const errorLocationInfo: LocationInfo = {
        country: 'Unknown',
        city: 'Unknown',
        isp: 'Unknown'
      };
      return errorLocationInfo;
    }
  }
  
  /**
   * 获取最佳测试服务器
   */
  async getBestServer(): Promise<TestServer> {
    const testServers: TestServer[] = [
      {
        id: 'cloudflare',
        name: 'Cloudflare',
        url: 'https://speed.cloudflare.com',
        location: 'Global CDN',
        latency: 0
      },
      {
        id: 'baidu',
        name: '百度',
        url: 'https://www.baidu.com',
        location: '北京',
        latency: 0
      },
      {
        id: 'tencent',
        name: '腾讯云',
        url: 'https://cloud.tencent.com',
        location: '深圳',
        latency: 0
      }
    ];
    
    // 测试每个服务器的延迟
    let bestServer = testServers[0];
    let bestLatency = Number.MAX_VALUE;
    
    for (const server of testServers) {
      try {
        const latency = await this.testServerLatency(server.url);
        server.latency = latency;
        
        if (latency < bestLatency) {
          bestLatency = latency;
          bestServer = server;
        }
      } catch (error) {
        console.error(`测试服务器 ${server.name} 延迟失败:`, error);
        server.latency = Number.MAX_VALUE;
      }
    }
    
    return bestServer;
  }
  
  /**
   * 测试服务器延迟
   */
  async testServerLatency(url: string): Promise<number> {
    const startTime = Date.now();
    
    try {
      const httpRequest = http.createHttp();
      await httpRequest.request(url, {
        method: http.RequestMethod.HEAD,
        connectTimeout: 5000,
        readTimeout: 5000
      });
      
      const latency = Date.now() - startTime;
      httpRequest.destroy();
      
      return latency;
    } catch (error) {
      console.error(`测试服务器延迟失败 ${url}:`, error);
      return Number.MAX_VALUE;
    }
  }
  
  /**
   * 获取网络质量评分
   */
  getNetworkQualityScore(downloadSpeed: number, uploadSpeed: number, latency: number): number {
    let score = 0;
    
    // 下载速度评分 (40%权重)
    if (downloadSpeed >= 100) {
      score += 40;
    } else if (downloadSpeed >= 50) {
      score += 35;
    } else if (downloadSpeed >= 25) {
      score += 30;
    } else if (downloadSpeed >= 10) {
      score += 20;
    } else if (downloadSpeed >= 5) {
      score += 10;
    }
    
    // 上传速度评分 (30%权重)
    if (uploadSpeed >= 50) {
      score += 30;
    } else if (uploadSpeed >= 25) {
      score += 25;
    } else if (uploadSpeed >= 10) {
      score += 20;
    } else if (uploadSpeed >= 5) {
      score += 15;
    } else if (uploadSpeed >= 1) {
      score += 10;
    }
    
    // 延迟评分 (30%权重)
    if (latency <= 20) {
      score += 30;
    } else if (latency <= 50) {
      score += 25;
    } else if (latency <= 100) {
      score += 20;
    } else if (latency <= 200) {
      score += 15;
    } else if (latency <= 500) {
      score += 10;
    }
    
    return Math.min(score, 100);
  }
  
  /**
   * 获取网络质量描述
   */
  getNetworkQualityDescription(score: number): string {
    if (score >= 90) {
      return '优秀';
    } else if (score >= 75) {
      return '良好';
    } else if (score >= 60) {
      return '一般';
    } else if (score >= 40) {
      return '较差';
    } else {
      return '很差';
    }
  }
  
  /**
   * 注册网络状态变化回调
   */
  async registerNetworkCallback(callback: (isConnected: boolean) => void): Promise<void> {
    try {
      // 简化实现，定期检查网络状态
      setInterval(async () => {
        const isConnected = await this.checkConnectivity();
        callback(isConnected);
      }, 5000);
    } catch (error) {
      console.error('注册网络状态监听失败:', error);
    }
  }
  
  /**
   * 取消网络状态变化回调
   */
  async unregisterNetworkCallback(): Promise<void> {
    try {
      // 简化实现，暂不支持取消
      console.log('网络监听已停止');
    } catch (error) {
      console.error('取消网络状态监听失败:', error);
    }
  }
  
  /**
   * 检查是否为计费网络
   */
  async isMeteredNetwork(): Promise<boolean> {
    try {
      const netHandle = await connection.getDefaultNet();
      return netHandle !== null;
    } catch (error) {
      console.error('检查计费网络失败:', error);
      return true; // 默认认为是计费网络，更安全
    }
  }
  
  /**
   * 获取网络类型
   */
  private getNetworkType(bearerTypes: number[]): string {
    if (bearerTypes.includes(connection.NetBearType.BEARER_WIFI)) {
      return 'WiFi';
    } else if (bearerTypes.includes(connection.NetBearType.BEARER_CELLULAR)) {
      return 'Cellular';
    } else {
      return 'Unknown';
    }
  }

  /**
   * 检测WiFi网络类型（区分普通WiFi和千兆WiFi）
   */
  private async detectWifiType(): Promise<NetworkType> {
    try {
      // 通过快速速度测试来判断WiFi类型
      const speedMbps = await this.performQuickSpeedTest();

      if (speedMbps >= 500) {
        console.log(`检测到千兆WiFi网络: ${speedMbps.toFixed(2)} Mbps`);
        return NetworkType.WIFI; // 可以考虑添加 WIFI_GIGABIT 类型
      } else {
        console.log(`检测到普通WiFi网络: ${speedMbps.toFixed(2)} Mbps`);
        return NetworkType.WIFI;
      }
    } catch (error) {
      console.warn('WiFi类型检测失败，使用默认类型:', error);
      return NetworkType.WIFI;
    }
  }

  /**
   * 检测蜂窝网络类型
   */
  private async detectCellularType(): Promise<NetworkType> {
    try {
      // 通过速度测试来判断蜂窝网络类型
      const speedMbps = await this.performQuickSpeedTest();

      if (speedMbps >= 100) {
        console.log(`检测到5G网络: ${speedMbps.toFixed(2)} Mbps`);
        return NetworkType.CELLULAR_5G;
      } else if (speedMbps >= 20) {
        console.log(`检测到4G网络: ${speedMbps.toFixed(2)} Mbps`);
        return NetworkType.CELLULAR_4G;
      } else {
        console.log(`检测到3G网络: ${speedMbps.toFixed(2)} Mbps`);
        return NetworkType.CELLULAR_3G;
      }
    } catch (error) {
      console.warn('蜂窝网络类型检测失败，使用默认4G:', error);
      return NetworkType.CELLULAR_4G;
    }
  }

  /**
   * 检测以太网类型（区分普通以太网和千兆以太网）
   */
  private async detectEthernetType(): Promise<NetworkType> {
    try {
      // 以太网通常是高速网络，直接返回
      console.log('检测到以太网连接，假设为千兆网络');
      return NetworkType.ETHERNET;
    } catch (error) {
      console.warn('以太网类型检测失败:', error);
      return NetworkType.ETHERNET;
    }
  }

  /**
   * 执行快速速度测试来判断网络类型
   */
  private async performQuickSpeedTest(): Promise<number> {
    try {
      const testUrl = 'https://speed.cloudflare.com/__down?bytes=5000000'; // 5MB测试文件
      const startTime = Date.now();

      const httpRequest = http.createHttp();
      let downloadedBytes = 0;

      httpRequest.on('dataReceive', (data) => {
        if (data instanceof ArrayBuffer) {
          downloadedBytes += data.byteLength;
        }
      });

      await httpRequest.request(testUrl, {
        method: http.RequestMethod.GET,
        expectDataType: http.HttpDataType.ARRAY_BUFFER,
        connectTimeout: 8000,
        readTimeout: 15000,
        header: {
          'User-Agent': 'HarmonyOS-NetworkDetection/1.0.0',
          'Cache-Control': 'no-cache'
        }
      });

      const duration = Date.now() - startTime;
      const speedMbps = (downloadedBytes * 8) / (duration * 1000); // Mbps

      httpRequest.destroy();

      console.log(`网络速度检测: ${speedMbps.toFixed(2)} Mbps (${downloadedBytes} bytes in ${duration}ms)`);
      return speedMbps;

    } catch (error) {
      console.warn('快速速度测试失败:', error);
      return 0; // 返回0表示无法检测
    }
  }

  /**
   * 网络质量评估（借鉴LibreSpeed的综合评估算法）
   */
  assessNetworkQuality(
    bandwidth: number,
    latency: number,
    jitter: number,
    packetLoss: number = 0
  ): NetworkQualityMetrics {
    let score = 0;

    // 带宽评分 (40% 权重)
    if (bandwidth >= 100) score += 40;
    else if (bandwidth >= 50) score += 30;
    else if (bandwidth >= 25) score += 25;
    else if (bandwidth >= 10) score += 20;
    else if (bandwidth >= 5) score += 15;
    else score += 10;

    // 延迟评分 (30% 权重)
    if (latency <= 20) score += 30;
    else if (latency <= 50) score += 25;
    else if (latency <= 100) score += 20;
    else if (latency <= 200) score += 15;
    else if (latency <= 500) score += 10;
    else score += 5;

    // 抖动评分 (20% 权重)
    if (jitter <= 5) score += 20;
    else if (jitter <= 10) score += 15;
    else if (jitter <= 20) score += 12;
    else if (jitter <= 50) score += 8;
    else score += 5;

    // 丢包率评分 (10% 权重)
    if (packetLoss <= 0.1) score += 10;
    else if (packetLoss <= 0.5) score += 8;
    else if (packetLoss <= 1.0) score += 6;
    else if (packetLoss <= 2.0) score += 4;
    else score += 2;

    // 确定质量等级
    let quality: 'excellent' | 'good' | 'fair' | 'poor';
    if (score >= 85) quality = 'excellent';
    else if (score >= 70) quality = 'good';
    else if (score >= 50) quality = 'fair';
    else quality = 'poor';

    const metrics: NetworkQualityMetrics = {
      bandwidth,
      latency,
      jitter,
      packetLoss,
      quality,
      score
    };

    console.log(`网络质量评估: ${quality} (${score}/100) - 带宽:${bandwidth}Mbps 延迟:${latency}ms 抖动:${jitter}ms`);
    return metrics;
  }

  /**
   * 获取网络质量建议
   */
  getNetworkQualityRecommendations(metrics: NetworkQualityMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.bandwidth < 10) {
      recommendations.push('建议升级到更高带宽的网络套餐');
    }

    if (metrics.latency > 100) {
      recommendations.push('延迟较高，建议检查网络连接或更换网络');
    }

    if (metrics.jitter > 20) {
      recommendations.push('网络抖动较大，可能影响实时应用体验');
    }

    if (metrics.packetLoss > 1.0) {
      recommendations.push('存在丢包现象，建议检查网络设备');
    }

    if (metrics.quality === 'excellent') {
      recommendations.push('网络质量优秀，适合各种网络应用');
    }

    return recommendations;
  }

  /**
   * 获取网络类型描述
   */
  getNetworkTypeDescription(networkType: string): string {
    switch (networkType) {
      case NetworkType.WIFI:
        return 'WiFi网络';
      case NetworkType.CELLULAR_5G:
        return '5G移动网络';
      case NetworkType.CELLULAR_4G:
        return '4G移动网络';
      case NetworkType.CELLULAR_3G:
        return '3G移动网络';
      case NetworkType.ETHERNET:
        return '以太网';
      default:
        return '未知网络';
    }
  }
}