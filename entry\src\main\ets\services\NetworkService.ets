// 网络服务
// 获取网络信息和连接状态

import connection from '@ohos.net.connection';
import http from '@ohos.net.http';
import { NetworkInfo, TestServer, NetworkType } from '../common/types/SpeedTestModels';
import { TypeConverter } from '../common/utils/TypeConverter';

/**
 * IP响应接口
 */
interface IpResponse {
  ip?: string;
}

/**
 * 位置信息接口
 */
interface LocationInfo {
  country: string;
  city: string;
  isp: string;
}

/**
 * 位置数据接口
 */
interface LocationData {
  country_name?: string;
  city?: string;
  org?: string;
}

/**
 * 网络服务类
 */
export class NetworkService {
  private readonly IP_API_URL = 'https://api.ipify.org?format=json';
  private readonly GEO_API_URL = 'https://ipapi.co/json/';
  
  /**
   * 获取网络连接信息
   */
  async getNetworkInfo(): Promise<NetworkInfo> {
    try {
      // 获取网络连接状态
      const netHandle = await connection.getDefaultNet();
      const netCapabilities = await connection.getNetCapabilities(netHandle);
      const connectionProperties = await connection.getConnectionProperties(netHandle);
      
      // 获取IP地址
      const ipAddress = await this.getPublicIP();
      
      // 判断网络类型
      let networkType = NetworkType.UNKNOWN;
      if (netCapabilities.bearerTypes && netCapabilities.bearerTypes.includes(connection.NetBearType.BEARER_WIFI)) {
        networkType = NetworkType.WIFI;
      } else if (netCapabilities.bearerTypes && netCapabilities.bearerTypes.includes(connection.NetBearType.BEARER_CELLULAR)) {
        networkType = NetworkType.CELLULAR_4G; // 默认4G，实际可能需要更详细的判断
      } else if (netCapabilities.bearerTypes && netCapabilities.bearerTypes.includes(connection.NetBearType.BEARER_ETHERNET)) {
        networkType = NetworkType.ETHERNET;
      }
      
      return {
        type: networkType,
        operator: 'Unknown', // 需要额外API获取运营商信息
        ipAddress: ipAddress,
        signalStrength: 0, // 需要额外API获取信号强度
        isConnected: netHandle !== null
      };
    } catch (error) {
      console.error('获取网络信息失败:', error);
      // 返回默认网络信息
      return {
        type: NetworkType.UNKNOWN,
        operator: 'Unknown',
        ipAddress: '0.0.0.0',
        signalStrength: 0,
        isConnected: false
      };
    }
  }
  
  /**
   * 检查网络连接状态
   */
  async checkConnectivity(): Promise<boolean> {
    try {
      const netHandle = await connection.getDefaultNet();
      return netHandle !== null;
    } catch (error) {
      console.error('检查网络连接失败:', error);
      return false;
    }
  }
  
  /**
   * 获取公网IP地址
   */
  async getPublicIP(): Promise<string> {
    try {
      const httpRequest = http.createHttp();
      const response = await httpRequest.request(this.IP_API_URL, {
        method: http.RequestMethod.GET,
        connectTimeout: 10000,
        readTimeout: 10000
      });
      
      httpRequest.destroy();
      
      if (response.result && typeof response.result === 'string') {
        const data: IpResponse = JSON.parse(response.result);
        return data.ip || '0.0.0.0';
      }
      
      return '0.0.0.0';
    } catch (error) {
      console.error('获取公网IP失败:', error);
      return '0.0.0.0';
    }
  }
  
  /**
   * 获取地理位置信息
   */
  async getLocationInfo(): Promise<LocationInfo> {
    try {
      const httpRequest = http.createHttp();
      const response = await httpRequest.request(this.GEO_API_URL, {
        method: http.RequestMethod.GET,
        connectTimeout: 10000,
        readTimeout: 10000
      });
      
      httpRequest.destroy();
      
      if (response.result && typeof response.result === 'string') {
        const data = JSON.parse(response.result as string) as LocationData;
        const locationInfo: LocationInfo = {
        country: data.country_name || 'Unknown',
        city: data.city || 'Unknown',
        isp: data.org || 'Unknown'
      };
      return locationInfo;
      }
      
      const defaultLocationInfo: LocationInfo = {
      country: 'Unknown',
      city: 'Unknown',
      isp: 'Unknown'
    };
    return defaultLocationInfo;
    } catch (error) {
      console.error('获取地理位置信息失败:', error);
      const errorLocationInfo: LocationInfo = {
        country: 'Unknown',
        city: 'Unknown',
        isp: 'Unknown'
      };
      return errorLocationInfo;
    }
  }
  
  /**
   * 获取最佳测试服务器
   */
  async getBestServer(): Promise<TestServer> {
    const testServers: TestServer[] = [
      {
        id: 'cloudflare',
        name: 'Cloudflare',
        url: 'https://speed.cloudflare.com',
        location: 'Global CDN',
        latency: 0
      },
      {
        id: 'baidu',
        name: '百度',
        url: 'https://www.baidu.com',
        location: '北京',
        latency: 0
      },
      {
        id: 'tencent',
        name: '腾讯云',
        url: 'https://cloud.tencent.com',
        location: '深圳',
        latency: 0
      }
    ];
    
    // 测试每个服务器的延迟
    let bestServer = testServers[0];
    let bestLatency = Number.MAX_VALUE;
    
    for (const server of testServers) {
      try {
        const latency = await this.testServerLatency(server.url);
        server.latency = latency;
        
        if (latency < bestLatency) {
          bestLatency = latency;
          bestServer = server;
        }
      } catch (error) {
        console.error(`测试服务器 ${server.name} 延迟失败:`, error);
        server.latency = Number.MAX_VALUE;
      }
    }
    
    return bestServer;
  }
  
  /**
   * 测试服务器延迟
   */
  async testServerLatency(url: string): Promise<number> {
    const startTime = Date.now();
    
    try {
      const httpRequest = http.createHttp();
      await httpRequest.request(url, {
        method: http.RequestMethod.HEAD,
        connectTimeout: 5000,
        readTimeout: 5000
      });
      
      const latency = Date.now() - startTime;
      httpRequest.destroy();
      
      return latency;
    } catch (error) {
      console.error(`测试服务器延迟失败 ${url}:`, error);
      return Number.MAX_VALUE;
    }
  }
  
  /**
   * 获取网络质量评分
   */
  getNetworkQualityScore(downloadSpeed: number, uploadSpeed: number, latency: number): number {
    let score = 0;
    
    // 下载速度评分 (40%权重)
    if (downloadSpeed >= 100) {
      score += 40;
    } else if (downloadSpeed >= 50) {
      score += 35;
    } else if (downloadSpeed >= 25) {
      score += 30;
    } else if (downloadSpeed >= 10) {
      score += 20;
    } else if (downloadSpeed >= 5) {
      score += 10;
    }
    
    // 上传速度评分 (30%权重)
    if (uploadSpeed >= 50) {
      score += 30;
    } else if (uploadSpeed >= 25) {
      score += 25;
    } else if (uploadSpeed >= 10) {
      score += 20;
    } else if (uploadSpeed >= 5) {
      score += 15;
    } else if (uploadSpeed >= 1) {
      score += 10;
    }
    
    // 延迟评分 (30%权重)
    if (latency <= 20) {
      score += 30;
    } else if (latency <= 50) {
      score += 25;
    } else if (latency <= 100) {
      score += 20;
    } else if (latency <= 200) {
      score += 15;
    } else if (latency <= 500) {
      score += 10;
    }
    
    return Math.min(score, 100);
  }
  
  /**
   * 获取网络质量描述
   */
  getNetworkQualityDescription(score: number): string {
    if (score >= 90) {
      return '优秀';
    } else if (score >= 75) {
      return '良好';
    } else if (score >= 60) {
      return '一般';
    } else if (score >= 40) {
      return '较差';
    } else {
      return '很差';
    }
  }
  
  /**
   * 注册网络状态变化回调
   */
  async registerNetworkCallback(callback: (isConnected: boolean) => void): Promise<void> {
    try {
      // 简化实现，定期检查网络状态
      setInterval(async () => {
        const isConnected = await this.checkConnectivity();
        callback(isConnected);
      }, 5000);
    } catch (error) {
      console.error('注册网络状态监听失败:', error);
    }
  }
  
  /**
   * 取消网络状态变化回调
   */
  async unregisterNetworkCallback(): Promise<void> {
    try {
      // 简化实现，暂不支持取消
      console.log('网络监听已停止');
    } catch (error) {
      console.error('取消网络状态监听失败:', error);
    }
  }
  
  /**
   * 检查是否为计费网络
   */
  async isMeteredNetwork(): Promise<boolean> {
    try {
      const netHandle = await connection.getDefaultNet();
      return netHandle !== null;
    } catch (error) {
      console.error('检查计费网络失败:', error);
      return true; // 默认认为是计费网络，更安全
    }
  }
  
  /**
   * 获取网络类型
   */
  private getNetworkType(bearerTypes: number[]): string {
    if (bearerTypes.includes(connection.NetBearType.BEARER_WIFI)) {
      return 'WiFi';
    } else if (bearerTypes.includes(connection.NetBearType.BEARER_CELLULAR)) {
      return 'Cellular';
    } else {
      return 'Unknown';
    }
  }

  /**
   * 获取网络类型描述
   */
  getNetworkTypeDescription(networkType: string): string {
    switch (networkType) {
      case NetworkType.WIFI:
        return 'WiFi网络';
      case NetworkType.CELLULAR_5G:
        return '5G移动网络';
      case NetworkType.CELLULAR_4G:
        return '4G移动网络';
      case NetworkType.CELLULAR_3G:
        return '3G移动网络';
      case NetworkType.ETHERNET:
        return '以太网';
      default:
        return '未知网络';
    }
  }
}