// 设置页面
// 应用设置和配置选项

import { StorageService } from '../services/StorageService';
import { NetworkService } from '../services/NetworkService';
import { TypeConverter } from '../common/utils/TypeConverter';
import router from '@ohos.router';
import { promptAction } from '@kit.ArkUI';
import { common } from '@kit.AbilityKit';

/**
 * Toast选项接口
 */
interface ToastOptions {
  message: string;
  duration: number;
}

/**
 * 对话框按钮接口
 */
interface DialogButton {
  text: string;
  color: string;
}

/**
 * 对话框选项接口
 */
interface DialogOptions {
  title: string;
  message: string;
  buttons: DialogButton[];
}

/**
 * 本地应用设置接口
 */
interface LocalAppSettings {
  autoStart: boolean;
  saveHistory: boolean;
  maxHistoryCount: number;
  testInterval: number;
  serverUrl: string;
}

@Entry
@Component
struct Settings {
  @State autoStart: boolean = false;
  @State saveHistory: boolean = true;
  @State maxHistoryCount: number = 100;
  @State testInterval: number = 30;
  @State serverUrl: string = 'https://speed.cloudflare.com';
  @State isLoading: boolean = true;
  
  private storageService = new StorageService();
  private networkService = new NetworkService();
  
  async aboutToAppear() {
    await this.loadSettings();
  }
  
  async loadSettings() {
    this.isLoading = true;
    
    try {
      // 获取应用上下文并初始化存储服务
      const uiContext = this.getUIContext();
      const context = uiContext.getHostContext() as common.UIAbilityContext;
      await this.storageService.initialize(context);

      // 加载设置
      const savedSettings = await this.storageService.getSettings();
      this.autoStart = savedSettings.autoStart || false;
      this.saveHistory = savedSettings.saveHistory !== undefined ? savedSettings.saveHistory : true;
      this.maxHistoryCount = savedSettings.maxHistoryCount || 100;
      this.testInterval = savedSettings.testInterval || 30;
      this.serverUrl = savedSettings.serverUrl || 'https://speed.cloudflare.com';
    } catch (error) {
      console.error('加载设置失败:', error);
    } finally {
      this.isLoading = false;
    }
  }
  
  async saveSettings() {
    try {
      // 使用单独的设置方法来避免类型问题
      await this.storageService.setSetting('autoStart', this.autoStart);
      await this.storageService.setSetting('saveHistory', this.saveHistory);
      await this.storageService.setSetting('maxHistoryCount', this.maxHistoryCount);
      await this.storageService.setSetting('testInterval', this.testInterval);
      await this.storageService.setSetting('serverUrl', this.serverUrl);

      const toastOptions: ToastOptions = {
        message: '设置已保存',
        duration: 2000
      };
      promptAction.showToast(toastOptions);
    } catch (error) {
      console.error('保存设置失败:', error);
      const errorToastOptions: ToastOptions = {
        message: '保存设置失败',
        duration: 2000
      };
      promptAction.showToast(errorToastOptions);
    }
  }
  
  navigateBack() {
    router.back();
  }
  
  async clearAllData() {
    const cancelButton: DialogButton = {
      text: '取消',
      color: '#007AFF'
    };

    const confirmButton: DialogButton = {
      text: '确定',
      color: '#FF3B30'
    };

    const dialogOptions: DialogOptions = {
      title: '清除所有数据',
      message: '确定要清除所有测试历史记录和设置吗？此操作不可恢复。',
      buttons: [cancelButton, confirmButton]
    };

    promptAction.showDialog(dialogOptions).then(async (data: promptAction.ShowDialogSuccessResponse) => {
      if (data.index === 1) {
        try {
          await this.storageService.clearAllTestHistory();
          const successToastOptions: ToastOptions = {
            message: '数据已清除',
            duration: 2000
          };
          promptAction.showToast(successToastOptions);
        } catch (error) {
          console.error('清除数据失败:', error);
          const errorToastOptions: ToastOptions = {
            message: '清除数据失败',
            duration: 2000
          };
          promptAction.showToast(errorToastOptions);
        }
      }
    });
  }
  
  build() {
    Column() {
      // 标题栏
      Row() {
        Button() {
          Text('← 返回')
            .fontSize(16)
            .fontColor('#007AFF')
        }
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.navigateBack();
        })
        
        Text('设置')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
        
        Button() {
          Text('保存')
            .fontSize(16)
            .fontColor('#007AFF')
        }
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.saveSettings();
        })
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 16, bottom: 16 })
      .justifyContent(FlexAlign.SpaceBetween)
      
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#007AFF')
          Text('加载中...')
            .fontSize(14)
            .fontColor('#666666')
            .margin({ top: 12 })
        }
        .layoutWeight(1)
        .width('100%')
        .justifyContent(FlexAlign.Center)
      } else {
        // 设置列表
        List({ space: 12 }) {
          // 基本设置
          ListItemGroup() {
            ListItem() {
              Row() {
                Text('保存测试历史')
                  .fontSize(16)
                  .fontColor('#333333')
                  .layoutWeight(1)
                
                Toggle({ type: ToggleType.Switch, isOn: this.saveHistory })
                  .onChange((isOn: boolean) => {
                    this.saveHistory = isOn;
                  })
              }
              .width('100%')
              .padding(16)
              .backgroundColor('#FFFFFF')
              .borderRadius(8)
            }
            
            ListItem() {
              Row() {
                Column() {
                  Text('最大历史记录数')
                    .fontSize(16)
                    .fontColor('#333333')
                    .alignSelf(ItemAlign.Start)
                  Text(`当前: ${this.maxHistoryCount} 条`)
                    .fontSize(12)
                    .fontColor('#666666')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)
                
                Text('>')
                  .fontSize(16)
                  .fontColor('#CCCCCC')
              }
              .width('100%')
              .padding(16)
              .backgroundColor('#FFFFFF')
              .borderRadius(8)
            }
            .onClick(() => {
              // 这里可以添加数字选择器
              const toastOptions: ToastOptions = {
                message: '功能开发中',
                duration: 2000
              };
              promptAction.showToast(toastOptions);
            })
          }
          
          // 测试设置
          ListItemGroup() {
            ListItem() {
              Row() {
                Column() {
                  Text('测试服务器')
                    .fontSize(16)
                    .fontColor('#333333')
                    .alignSelf(ItemAlign.Start)
                  Text(this.serverUrl)
                    .fontSize(12)
                    .fontColor('#666666')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)
                
                Text('>')
                  .fontSize(16)
                  .fontColor('#CCCCCC')
              }
              .width('100%')
              .padding(16)
              .backgroundColor('#FFFFFF')
              .borderRadius(8)
            }
            .onClick(() => {
              // 这里可以添加服务器选择
              const toastOptions: ToastOptions = {
                message: '功能开发中',
                duration: 2000
              };
              promptAction.showToast(toastOptions);
            })
          }
          
          // 数据管理
          ListItemGroup() {
            ListItem() {
              Row() {
                Text('清除所有数据')
                  .fontSize(16)
                  .fontColor('#FF3B30')
                  .layoutWeight(1)
                
                Text('>')
                  .fontSize(16)
                  .fontColor('#CCCCCC')
              }
              .width('100%')
              .padding(16)
              .backgroundColor('#FFFFFF')
              .borderRadius(8)
            }
            .onClick(() => {
              this.clearAllData();
            })
          }
          
          // 关于
          ListItemGroup() {
            ListItem() {
              Row() {
                Text('应用版本')
                  .fontSize(16)
                  .fontColor('#333333')
                  .layoutWeight(1)
                
                Text('1.0.0')
                  .fontSize(16)
                  .fontColor('#666666')
              }
              .width('100%')
              .padding(16)
              .backgroundColor('#FFFFFF')
              .borderRadius(8)
            }
          }
        }
        .layoutWeight(1)
        .width('100%')
        .padding({ left: 16, right: 16 })
        .scrollBar(BarState.Auto)
      }
    }
    .height('100%')
    .width('100%')
    .backgroundColor('#F2F2F7')
  }
}
