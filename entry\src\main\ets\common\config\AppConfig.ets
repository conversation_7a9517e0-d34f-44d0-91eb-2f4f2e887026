// 应用配置管理类
// 统一管理应用的各种配置参数

/**
 * 测试服务器配置接口
 */
export interface TestServerConfig {
  id: string;
  name: string;
  location: string;
  downloadUrl: string;
  uploadUrl: string;
  latencyUrl: string;
}

/**
 * 超时配置接口
 */
export interface TimeoutSettings {
  latency: number;
  download: number;
  upload: number;
  jitter: number;
}

/**
 * 缓存配置接口
 */
export interface CacheSettings {
  enabled: boolean;
  duration: number; // 毫秒
  maxSize: number;  // 最大缓存项数
}

/**
 * 重试配置接口
 */
export interface RetrySettings {
  maxRetries: number;
  baseDelay: number; // 基础延迟时间（毫秒）
  maxDelay: number;  // 最大延迟时间（毫秒）
}

/**
 * 应用配置管理类
 */
export class AppConfig {
  /**
   * 默认测试服务器列表 - 基于error.log分析优化
   */
  static readonly TEST_SERVERS: TestServerConfig[] = [
    {
      id: 'cloudflare-1',
      name: 'Cloudflare High Speed',
      location: 'Global CDN',
      downloadUrl: 'https://speed.cloudflare.com/__down?bytes=10000000', // 10MB - 确保足够测试时间
      uploadUrl: 'https://speed.cloudflare.com/__up',
      latencyUrl: 'https://speed.cloudflare.com/cdn-cgi/trace'
    },
    {
      id: 'cloudflare-2',
      name: 'Cloudflare Medium',
      location: 'Global CDN',
      downloadUrl: 'https://speed.cloudflare.com/__down?bytes=5000000', // 5MB标准测试
      uploadUrl: 'https://speed.cloudflare.com/__up',
      latencyUrl: 'https://speed.cloudflare.com/cdn-cgi/trace'
    },
    {
      id: 'cloudflare-3',
      name: 'Cloudflare Safe',
      location: 'Global CDN',
      downloadUrl: 'https://speed.cloudflare.com/__down?bytes=2000000', // 2MB兼容性测试
      uploadUrl: 'https://speed.cloudflare.com/__up',
      latencyUrl: 'https://speed.cloudflare.com/cdn-cgi/trace'
    },
    {
      id: 'fast-com',
      name: 'Fast.com (Netflix)',
      location: 'Netflix CDN',
      downloadUrl: 'https://api.fast.com/netflix/speedtest/v2.1/getUrls?https=true&token=YXNkZmFzZGxmbnNkYWZoYXNkZmhrYWxm&urlCount=1',
      uploadUrl: 'https://api.fast.com/netflix/speedtest/v2.1/getUrls?https=true&token=YXNkZmFzZGxmbnNkYWZoYXNkZmhrYWxm&urlCount=1',
      latencyUrl: 'https://fast.com'
    }
  ];

  /**
   * 默认超时设置 - 基于error.log分析优化
   */
  static readonly DEFAULT_TIMEOUTS: TimeoutSettings = {
    latency: 5000,    // 5秒 - 增加延迟测试超时，应对网络不稳定
    download: 60000,  // 60秒 - 大幅增加下载超时，避免大文件测试中断
    upload: 45000,    // 45秒 - 增加上传超时，确保测试完成
    jitter: 3000      // 3秒 - 增加抖动测试单次超时
  };

  /**
   * 缓存设置
   */
  static readonly CACHE_SETTINGS: CacheSettings = {
    enabled: true,
    duration: 30000,  // 30秒
    maxSize: 100      // 最多缓存100项
  };

  /**
   * 重试设置
   */
  static readonly RETRY_SETTINGS: RetrySettings = {
    maxRetries: 2,
    baseDelay: 1000,  // 1秒
    maxDelay: 5000    // 5秒
  };

  /**
   * 应用版本信息
   */
  static readonly APP_NAME = '网速测试';
  static readonly APP_VERSION = '1.0.0';
  static readonly APP_AUTHOR = 'HarmonyOS Developer';

  /**
   * 数据存储配置
   */
  static readonly MAX_HISTORY_RECORDS = 1000;
  static readonly AUTO_CLEANUP_DAYS = 30;
  static readonly EXPORT_FORMAT = 'json';

  /**
   * UI配置
   */
  static readonly ANIMATION_DURATION = 300;
  static readonly REFRESH_INTERVAL = 1000;
  static readonly CHART_UPDATE_INTERVAL = 100;
  static readonly LOADING_TIMEOUT = 30000;

  /**
   * 网络配置
   */
  static readonly USER_AGENT = 'HarmonyOS-SpeedTest/1.0.0';
  static readonly MAX_CONCURRENT_TESTS = 1;
  static readonly TEST_DATA_SIZE_SMALL = 1024 * 1024;      // 1MB
  static readonly TEST_DATA_SIZE_MEDIUM = 10 * 1024 * 1024; // 10MB
  static readonly TEST_DATA_SIZE_LARGE = 25 * 1024 * 1024;  // 25MB

  /**
   * 获取默认测试服务器
   */
  static getDefaultServer(): TestServerConfig {
    return AppConfig.TEST_SERVERS[0];
  }

  /**
   * 根据ID获取测试服务器
   */
  static getServerById(id: string): TestServerConfig | null {
    const server = AppConfig.TEST_SERVERS.find(s => s.id === id);
    return server || null;
  }

  /**
   * 获取所有可用的测试服务器
   */
  static getAllServers(): TestServerConfig[] {
    return AppConfig.TEST_SERVERS.slice();
  }

  /**
   * 验证配置是否有效
   */
  static validateConfig(): boolean {
    try {
      // 验证服务器配置
      if (!AppConfig.TEST_SERVERS || AppConfig.TEST_SERVERS.length === 0) {
        return false;
      }

      // 验证超时配置
      const timeouts = AppConfig.DEFAULT_TIMEOUTS;
      if (timeouts.latency <= 0 || timeouts.download <= 0 ||
          timeouts.upload <= 0 || timeouts.jitter <= 0) {
        return false;
      }

      // 验证重试配置
      const retry = AppConfig.RETRY_SETTINGS;
      if (retry.maxRetries < 0 || retry.baseDelay <= 0 || retry.maxDelay <= 0) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('配置验证失败:', error);
      return false;
    }
  }

  /**
   * 获取配置摘要信息
   */
  static getConfigSummary(): string {
    return `${AppConfig.APP_NAME} v${AppConfig.APP_VERSION} - ` +
           `${AppConfig.TEST_SERVERS.length}个测试服务器，` +
           `最大重试${AppConfig.RETRY_SETTINGS.maxRetries}次`;
  }
}
