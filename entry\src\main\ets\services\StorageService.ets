// 存储服务
// 用于保存和管理测试历史记录

import preferences from '@ohos.data.preferences';
import { common } from '@kit.AbilityKit';
import { SpeedTestResult } from '../common/types/SpeedTestModels';
import { TypeConverter } from '../common/utils/TypeConverter';
import { DateUtil } from '../common/utils/DateUtil';

/**
 * 统计信息接口
 */
interface TestStatistics {
  totalTests: number;
  averageDownloadSpeed: number;
  averageUploadSpeed: number;
  averageLatency: number;
  bestDownloadSpeed: number;
  bestUploadSpeed: number;
  bestLatency: number;
}

/**
 * 存储使用情况接口
 */
interface StorageUsage {
  historyCount: number;
  estimatedSize: number;
}

/**
 * 导出数据接口
 */
interface ExportData {
  version: string;
  timestamp: number;
  data: SpeedTestResult[];
}

/**
 * 导入数据接口
 */
interface ImportData {
  data?: SpeedTestResult[];
  history?: SpeedTestResult[];
}

/**
 * 应用设置接口
 */
interface AppSettings {
  autoStart: boolean;
  saveHistory: boolean;
  maxHistoryCount: number;
  testInterval: number;
  serverUrl: string;
}

/**
 * 存储服务类
 */
export class StorageService {
  private readonly PREFERENCES_NAME = 'speed_test_data';
  private readonly HISTORY_KEY = 'test_history';
  private readonly SETTINGS_KEY = 'app_settings';
  private readonly MAX_HISTORY_COUNT = 100; // 最大保存100条历史记录

  private preferencesInstance: preferences.Preferences | null = null;
  private context: common.UIAbilityContext | null = null;
  
  /**
   * 设置上下文
   */
  setContext(context: common.UIAbilityContext): void {
    this.context = context;
  }

  /**
   * 初始化存储服务
   */
  async initialize(context?: common.UIAbilityContext): Promise<void> {
    try {
      // 使用传入的上下文或已设置的上下文
      const appContext = context || this.context;
      if (!appContext) {
        throw new Error('未设置应用上下文');
      }
      this.preferencesInstance = await preferences.getPreferences(appContext, this.PREFERENCES_NAME);
    } catch (error) {
      console.error('初始化存储服务失败:', error);
      throw new Error('存储服务初始化失败');
    }
  }
  
  /**
   * 保存测试结果
   */
  async saveTestResult(result: SpeedTestResult): Promise<void> {
    if (!this.preferencesInstance) {
      await this.initialize();
    }
    
    try {
      // 获取现有历史记录
      const history = await this.getTestHistory();
      
      // 添加新记录到开头
      history.unshift(result);
      
      // 限制历史记录数量
      if (history.length > this.MAX_HISTORY_COUNT) {
        history.splice(this.MAX_HISTORY_COUNT);
      }
      
      // 保存更新后的历史记录
      const historyJson = JSON.stringify(history);
      await this.preferencesInstance!.put(this.HISTORY_KEY, historyJson);
      await this.preferencesInstance!.flush();
      
      console.log('测试结果保存成功');
    } catch (error) {
      console.error('保存测试结果失败:', error);
      throw new Error('保存测试结果失败');
    }
  }
  
  /**
   * 获取测试历史记录
   */
  async getTestHistory(): Promise<SpeedTestResult[]> {
    if (!this.preferencesInstance) {
      await this.initialize();
    }
    
    try {
      const historyJson = await this.preferencesInstance!.get(this.HISTORY_KEY, '[]') as string;
      const historyData = JSON.parse(historyJson) as Record<string, string | number | boolean>[];
      
      // 验证和转换数据
      const validHistory: SpeedTestResult[] = [];
      for (const item of historyData) {
        if (item && typeof item === 'object') {
          const validItem = TypeConverter.convertToSpeedTestResult(item as Record<string, string | number | boolean>);
          validHistory.push(validItem);
        }
      }
      
      return validHistory;
    } catch (error) {
      console.error('获取测试历史失败:', error);
      return [];
    }
  }
  
  /**
   * 获取指定日期范围的测试记录
   */
  async getTestHistoryByDateRange(startDate: Date, endDate: Date): Promise<SpeedTestResult[]> {
    const allHistory = await this.getTestHistory();
    const startTime = startDate.getTime();
    const endTime = endDate.getTime();
    
    return allHistory.filter(result => {
      const testTime = result.timestamp;
      return testTime >= startTime && testTime <= endTime;
    });
  }
  
  /**
   * 获取今日测试记录
   */
  async getTodayTestHistory(): Promise<SpeedTestResult[]> {
    const todayStart = DateUtil.getTodayStart();
    const todayEnd = DateUtil.getTodayEnd();
    return this.getTestHistoryByDateRange(new Date(todayStart), new Date(todayEnd));
  }
  
  /**
   * 获取本周测试记录
   */
  async getThisWeekTestHistory(): Promise<SpeedTestResult[]> {
    const weekStart = DateUtil.getThisWeekStart();
    const weekEnd = DateUtil.getThisWeekEnd();
    return this.getTestHistoryByDateRange(new Date(weekStart), new Date(weekEnd));
  }
  
  /**
   * 获取本月测试记录
   */
  async getThisMonthTestHistory(): Promise<SpeedTestResult[]> {
    const monthStart = DateUtil.getThisMonthStart();
    const monthEnd = DateUtil.getThisMonthEnd();
    return this.getTestHistoryByDateRange(new Date(monthStart), new Date(monthEnd));
  }
  
  /**
   * 删除指定的测试记录
   */
  async deleteTestResult(resultId: string): Promise<void> {
    if (!this.preferencesInstance) {
      await this.initialize();
    }
    
    try {
      const history = await this.getTestHistory();
      const filteredHistory = history.filter(result => result.id !== resultId);
      
      const historyJson = JSON.stringify(filteredHistory);
      await this.preferencesInstance!.put(this.HISTORY_KEY, historyJson);
      await this.preferencesInstance!.flush();
      
      console.log('测试记录删除成功');
    } catch (error) {
      console.error('删除测试记录失败:', error);
      throw new Error('删除测试记录失败');
    }
  }
  
  /**
   * 清空所有测试历史记录
   */
  async clearAllTestHistory(): Promise<void> {
    if (!this.preferencesInstance) {
      await this.initialize();
    }
    
    try {
      await this.preferencesInstance!.put(this.HISTORY_KEY, '[]');
      await this.preferencesInstance!.flush();
      
      console.log('测试历史记录清空成功');
    } catch (error) {
      console.error('清空测试历史记录失败:', error);
      throw new Error('清空测试历史记录失败');
    }
  }
  
  /**
   * 获取测试统计信息
   */
  async getTestStatistics(): Promise<TestStatistics> {
    try {
      const history = await this.getTestHistory();
      
      if (history.length === 0) {
        return {
          totalTests: 0,
          averageDownloadSpeed: 0,
          averageUploadSpeed: 0,
          averageLatency: 0,
          bestDownloadSpeed: 0,
          bestUploadSpeed: 0,
          bestLatency: 0
        };
      }
      
      let totalDownload = 0;
      let totalUpload = 0;
      let totalLatency = 0;
      let bestDownload = 0;
      let bestUpload = 0;
      let bestLatency = Number.MAX_VALUE;
      
      for (const result of history) {
        totalDownload += result.downloadSpeed;
        totalUpload += result.uploadSpeed;
        totalLatency += result.latency;
        
        if (result.downloadSpeed > bestDownload) {
          bestDownload = result.downloadSpeed;
        }
        
        if (result.uploadSpeed > bestUpload) {
          bestUpload = result.uploadSpeed;
        }
        
        if (result.latency < bestLatency) {
          bestLatency = result.latency;
        }
      }
      
      return {
        totalTests: history.length,
        averageDownloadSpeed: totalDownload / history.length,
        averageUploadSpeed: totalUpload / history.length,
        averageLatency: totalLatency / history.length,
        bestDownloadSpeed: bestDownload,
        bestUploadSpeed: bestUpload,
        bestLatency: bestLatency === Number.MAX_VALUE ? 0 : bestLatency
      };
    } catch (error) {
      console.error('获取测试统计失败:', error);
      throw new Error('获取测试统计失败');
    }
  }
  
  /**
   * 保存应用设置
   */
  async saveSettings(settings: AppSettings): Promise<void> {
    if (!this.preferencesInstance) {
      await this.initialize();
    }
    
    try {
      const settingsJson = JSON.stringify(settings);
      await this.preferencesInstance!.put(this.SETTINGS_KEY, settingsJson);
      await this.preferencesInstance!.flush();
      
      console.log('应用设置保存成功');
    } catch (error) {
      console.error('保存应用设置失败:', error);
      throw new Error('保存应用设置失败');
    }
  }
  
  /**
   * 获取应用设置
   */
  async getSettings(): Promise<AppSettings> {
    if (!this.preferencesInstance) {
      await this.initialize();
    }
    
    try {
      const settingsJson = await this.preferencesInstance!.get(this.SETTINGS_KEY, '{}') as string;
      return JSON.parse(settingsJson) as AppSettings;
    } catch (error) {
      console.error('获取应用设置失败:', error);
      return this.getDefaultSettings();
    }
  }
  
  /**
   * 获取默认设置
   */
  private getDefaultSettings(): AppSettings {
    return {
      autoStart: false,
      saveHistory: true,
      maxHistoryCount: 1000,
      testInterval: 30,
      serverUrl: 'https://speed.cloudflare.com'
    };
  }
  
  /**
   * 获取单个设置值
   */
  async getSetting(key: string, defaultValue: string | number | boolean = ''): Promise<string | number | boolean> {
    const settings = await this.getSettings();
    switch (key) {
      case 'autoStart':
        return settings.autoStart !== undefined ? settings.autoStart : defaultValue;
      case 'saveHistory':
        return settings.saveHistory !== undefined ? settings.saveHistory : defaultValue;
      case 'maxHistoryCount':
        return settings.maxHistoryCount !== undefined ? settings.maxHistoryCount : defaultValue;
      case 'testInterval':
        return settings.testInterval !== undefined ? settings.testInterval : defaultValue;
      case 'serverUrl':
        return settings.serverUrl !== undefined ? settings.serverUrl : defaultValue;
      default:
        return defaultValue;
    }
  }
  
  /**
   * 设置单个设置值
   */
  async setSetting(key: string, value: string | number | boolean): Promise<void> {
    const settings = await this.getSettings();
    await this.saveSettings({
      autoStart: key === 'autoStart' ? value as boolean : settings.autoStart,
      saveHistory: key === 'saveHistory' ? value as boolean : settings.saveHistory,
      maxHistoryCount: key === 'maxHistoryCount' ? value as number : settings.maxHistoryCount,
      testInterval: key === 'testInterval' ? value as number : settings.testInterval,
      serverUrl: key === 'serverUrl' ? value as string : settings.serverUrl
    });
  }
  
  /**
   * 导出测试历史数据
   */
  async exportTestHistory(): Promise<string> {
    const history = await this.getTestHistory();
    const statistics = await this.getTestStatistics();
    
    return JSON.stringify({
      exportTime: new Date().toISOString(),
      statistics: statistics,
      history: history
    }, null, 2);
  }
  
  /**
   * 导出历史数据
   */
  async exportHistory(): Promise<ExportData> {
    const history = await this.getTestHistory();
    return {
      version: '1.0.0',
      timestamp: Date.now(),
      data: history
    };
  }
  
  /**
   * 导入历史数据
   */
  async importHistory(data: ExportData): Promise<void> {
    try {
      // 验证导入的数据格式
      if (!data || typeof data !== 'object' || !data.data) {
        throw new Error('无效的导入数据格式');
      }

      // 验证版本兼容性
      if (!data.version || typeof data.version !== 'string') {
        throw new Error('缺少版本信息');
      }

      // 验证时间戳
      if (!data.timestamp || typeof data.timestamp !== 'number') {
        throw new Error('缺少时间戳信息');
      }

      // 验证数据数组
      if (!Array.isArray(data.data)) {
        throw new Error('导入数据格式错误：数据不是数组');
      }

      // 验证每个测试结果的格式
      const validResults: SpeedTestResult[] = [];
      for (const item of data.data) {
        if (this.isValidTestResult(item)) {
          validResults.push(item);
        } else {
          console.warn('跳过无效的测试结果:', item);
        }
      }

      if (validResults.length === 0) {
        throw new Error('导入数据中没有有效的测试结果');
      }

      // 保存验证后的数据
      if (this.preferencesInstance) {
        await this.preferencesInstance.put(this.HISTORY_KEY, JSON.stringify(validResults));
        await this.preferencesInstance.flush();
        console.log(`成功导入 ${validResults.length} 条测试记录`);
      }
    } catch (error) {
      console.error('导入历史数据失败:', error);
      throw new Error('导入历史数据失败: ' + (error as Error).message);
    }
  }
  
  /**
   * 导入测试历史数据
   */
  async importTestHistory(jsonData: string): Promise<void> {
    try {
      // 验证JSON格式
      if (!jsonData || typeof jsonData !== 'string' || jsonData.trim() === '') {
        throw new Error('导入数据为空或格式错误');
      }

      let importData: ImportData;
      try {
        importData = JSON.parse(jsonData) as ImportData;
      } catch (parseError) {
        throw new Error('JSON格式错误，无法解析导入数据');
      }

      // 验证导入数据结构
      if (!importData || typeof importData !== 'object') {
        throw new Error('导入数据结构错误');
      }

      // 支持两种格式：{ history: [] } 或 { data: [] }
      const historyData = importData.history || importData.data;

      if (!historyData || !Array.isArray(historyData)) {
        throw new Error('导入数据中缺少有效的历史记录数组');
      }

      if (historyData.length === 0) {
        throw new Error('导入数据中没有历史记录');
      }

      // 验证导入的数据
      const validHistory: SpeedTestResult[] = [];
      let invalidCount = 0;

      for (const item of historyData) {
        if (this.isValidTestResult(item)) {
          validHistory.push(item);
        } else {
          invalidCount++;
          console.warn('跳过无效的测试结果:', item);
        }
      }

      if (validHistory.length === 0) {
        throw new Error(`所有 ${historyData.length} 条记录都无效，无法导入`);
      }

      // 合并现有历史记录
      const currentHistory = await this.getTestHistory();
      const mergedHistory = [...validHistory, ...currentHistory];

      // 去重并限制数量
      const uniqueHistory = this.removeDuplicateResults(mergedHistory);
      if (uniqueHistory.length > this.MAX_HISTORY_COUNT) {
        uniqueHistory.splice(this.MAX_HISTORY_COUNT);
      }

      // 保存合并后的历史记录
      const historyJson = JSON.stringify(uniqueHistory);
      await this.preferencesInstance!.put(this.HISTORY_KEY, historyJson);
      await this.preferencesInstance!.flush();

      const message = `成功导入 ${validHistory.length} 条记录` +
        (invalidCount > 0 ? `，跳过 ${invalidCount} 条无效记录` : '');
      console.log('测试历史数据导入成功:', message);
    } catch (error) {
      console.error('导入测试历史数据失败:', error);
      throw new Error('导入测试历史数据失败: ' + (error as Error).message);
    }
  }
  
  /**
   * 验证测试结果数据的有效性
   */
  private isValidTestResult(result: SpeedTestResult): boolean {
    try {
      // 基本类型检查
      if (!result || typeof result !== 'object') {
        return false;
      }

      // 必需字段检查
      if (typeof result.id !== 'string' || result.id.trim() === '') {
        return false;
      }

      if (typeof result.timestamp !== 'number' || result.timestamp <= 0) {
        return false;
      }

      if (typeof result.downloadSpeed !== 'number' || result.downloadSpeed < 0) {
        return false;
      }

      if (typeof result.uploadSpeed !== 'number' || result.uploadSpeed < 0) {
        return false;
      }

      if (typeof result.latency !== 'number' || result.latency < 0) {
        return false;
      }

      // 可选字段检查
      if (result.jitter !== undefined && (typeof result.jitter !== 'number' || result.jitter < 0)) {
        return false;
      }

      // 检查网络信息对象
      if (result.networkInfo && typeof result.networkInfo === 'object') {
        if (typeof result.networkInfo.type !== 'string') {
          return false;
        }
      }

      // 检查测试服务器对象
      if (result.testServer && typeof result.testServer === 'object') {
        if (typeof result.testServer.url !== 'string') {
          return false;
        }
      }

      // 时间戳合理性检查（不能是未来时间，不能太久远）
      const now = Date.now();
      const oneYearAgo = now - (365 * 24 * 60 * 60 * 1000);
      if (result.timestamp > now || result.timestamp < oneYearAgo) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('验证测试结果时发生错误:', error);
      return false;
    }
  }
  
  /**
   * 去除重复的测试结果
   */
  private removeDuplicateResults(results: SpeedTestResult[]): SpeedTestResult[] {
    const uniqueResults: SpeedTestResult[] = [];
    const seenIds = new Set<string>();
    
    for (const result of results) {
      if (!seenIds.has(result.id)) {
        seenIds.add(result.id);
        uniqueResults.push(result);
      }
    }
    
    return uniqueResults;
  }
  
  /**
   * 获取存储使用情况
   */
  async getStorageUsage(): Promise<StorageUsage> {
    try {
      const history = await this.getTestHistory();
      const historyJson = JSON.stringify(history);
      
      return {
        historyCount: history.length,
        estimatedSize: historyJson.length // 字节数估算
      };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      throw new Error('获取存储使用情况失败');
    }
  }
  
  /**
   * 清理过期数据
   */
  async cleanupExpiredData(daysToKeep: number = 30): Promise<void> {
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    const history = await this.getTestHistory();
    
    const filteredHistory = history.filter(result => result.timestamp >= cutoffTime);
    
    if (filteredHistory.length !== history.length) {
      const historyJson = JSON.stringify(filteredHistory);
      await this.preferencesInstance!.put(this.HISTORY_KEY, historyJson);
      await this.preferencesInstance!.flush();
      
      console.log(`清理了 ${history.length - filteredHistory.length} 条过期数据`);
    }
  }
}