// 测速相关数据模型定义
// 遵循ArkTS规范，避免使用any类型和对象字面量类型

/**
 * 测速结果接口（增强版 - 包含质量评估）
 */
export interface SpeedTestResult {
  id: string;
  timestamp: number;
  downloadSpeed: number; // Mbps
  uploadSpeed: number;   // Mbps
  latency: number;       // ms
  jitter: number;        // ms
  packetLoss: number;    // %
  networkInfo: NetworkInfo;
  testServer: TestServer;
  location: string;
  duration: number;      // 测试持续时间(秒)
  // 新增质量评估字段
  qualityScore?: number;        // 0-100的质量评分
  reliability?: TestReliability; // 可信度评估
  warnings?: string[];          // 测试警告信息
}

/**
 * 测试可信度评估接口
 */
export interface TestReliability {
  downloadReliable: boolean;    // 下载测试是否可信
  uploadReliable: boolean;      // 上传测试是否可信
  latencyReliable: boolean;     // 延迟测试是否可信
  jitterReliable: boolean;      // 抖动测试是否可信
  overallScore: number;         // 总体可信度评分 (0-100)
  issues: string[];             // 发现的问题列表
}

/**
 * 网络环境检测结果接口
 */
export interface NetworkEnvironment {
  estimatedBandwidth: number;
  recommendedFileSize: number;
  networkQuality: 'excellent' | 'good' | 'fair' | 'poor';
}

/**
 * 网络信息接口
 */
export interface NetworkInfo {
  type: string;          // WiFi, 4G, 5G
  operator: string;      // 运营商
  ipAddress: string;
  signalStrength: number;
  isConnected: boolean;
}

/**
 * 测试服务器接口
 */
export interface TestServer {
  id: string;
  name: string;
  url: string;
  location: string;
  latency: number;
}

/**
 * 测试进度接口
 */
export interface TestProgress {
  phase: string;         // 'latency', 'download', 'upload'
  progress: number;      // 0-100
  currentSpeed: number;  // 当前速度 Mbps
  message: string;
}

/**
 * 完整测试结果接口
 */
export interface FullTestResult {
  downloadSpeed: number;
  uploadSpeed: number;
  latency: number;
  jitter: number;
  networkInfo: NetworkInfo;
  testServer: TestServer;
  timestamp: number;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  message: string;
  value?: number;
}

/**
 * 保存结果接口
 */
export interface SaveResult {
  success: boolean;
  message: string;
}

/**
 * 图表数据项接口
 */
export interface ChartDataItem {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

/**
 * 月度统计接口
 */
export interface MonthlyStats {
  totalTests: number;
  averageDownload: number;
  averageUpload: number;
  averageLatency: number;
  bestDownload: number;
  bestUpload: number;
  worstLatency: number;
}

/**
 * 测试配置接口
 */
export interface TestConfig {
  testDuration: number;     // 测试持续时间(秒)
  testFileSize: number;     // 测试文件大小(MB)
  maxRetries: number;       // 最大重试次数
  timeout: number;          // 超时时间(ms)
  selectedServer?: TestServer;
}

/**
 * 历史记录查询接口
 */
export interface HistoryQuery {
  startDate?: number;
  endDate?: number;
  networkType?: string;
  limit?: number;
  offset?: number;
}

/**
 * 测试状态枚举
 */
export enum TestStatus {
  IDLE = 'idle',
  TESTING_LATENCY = 'testing_latency',
  TESTING_DOWNLOAD = 'testing_download',
  TESTING_UPLOAD = 'testing_upload',
  TESTING_JITTER = 'testing_jitter',
  COMPLETED = 'completed',
  ERROR = 'error'
}

/**
 * 网络类型枚举
 */
export enum NetworkType {
  WIFI = 'WiFi',
  CELLULAR_4G = '4G',
  CELLULAR_5G = '5G',
  CELLULAR_3G = '3G',
  ETHERNET = 'Ethernet',
  UNKNOWN = 'Unknown'
}

/**
 * 测试阶段枚举
 */
export enum TestPhase {
  LATENCY = 'latency',
  DOWNLOAD = 'download',
  UPLOAD = 'upload',
  JITTER = 'jitter',
  COMPLETED = 'completed'
}