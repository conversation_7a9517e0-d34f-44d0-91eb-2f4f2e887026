// 高级优化功能综合验证脚本
// 验证智能服务器选择和自适应并发测试的集成效果

import { SpeedTestService } from './entry/src/main/ets/services/SpeedTestService';
import { ServerSelectionService } from './entry/src/main/ets/services/ServerSelectionService';
import { ConcurrentTestService } from './entry/src/main/ets/services/ConcurrentTestService';
import { NetworkService } from './entry/src/main/ets/services/NetworkService';
import { TestProgress, NetworkType } from './entry/src/main/ets/common/types/SpeedTestModels';

/**
 * 高级优化验证主函数
 */
async function main() {
  console.log('=== 高级优化功能综合验证 ===');
  console.log('验证智能服务器选择和自适应并发测试的集成效果...\n');

  const speedTestService = new SpeedTestService();
  const serverSelectionService = new ServerSelectionService();
  const concurrentTestService = new ConcurrentTestService();
  const networkService = new NetworkService();

  try {
    // 1. 网络环境检测
    console.log('1. 网络环境检测');
    console.log('==================');
    await testNetworkEnvironmentDetection(networkService);
    console.log('');

    // 2. 智能服务器选择验证
    console.log('2. 智能服务器选择验证');
    console.log('========================');
    await testIntelligentServerSelection(serverSelectionService);
    console.log('');

    // 3. 自适应并发测试验证
    console.log('3. 自适应并发测试验证');
    console.log('========================');
    await testAdaptiveConcurrentTesting(concurrentTestService, networkService);
    console.log('');

    // 4. 集成优化效果验证
    console.log('4. 集成优化效果验证');
    console.log('====================');
    await testIntegratedOptimizations(speedTestService);
    console.log('');

    // 5. 性能对比分析
    console.log('5. 性能对比分析');
    console.log('==================');
    await performAdvancedPerformanceAnalysis(speedTestService);
    console.log('');

    // 6. 稳定性和可靠性测试
    console.log('6. 稳定性和可靠性测试');
    console.log('======================');
    await testStabilityAndReliability(speedTestService);
    console.log('');

    // 7. 资源使用效率测试
    console.log('7. 资源使用效率测试');
    console.log('====================');
    await testResourceEfficiency(concurrentTestService);
    console.log('');

    console.log('🎉 高级优化功能验证完成！');
    console.log('智能服务器选择和自适应并发测试功能已全面验证。');

  } catch (error) {
    console.error('❌ 高级优化验证过程中发生错误:', error);
  } finally {
    // 清理资源
    speedTestService.stopTest();
    concurrentTestService.stopTest();
    serverSelectionService.cleanupCache();
    networkService.clearCache();
  }
}

/**
 * 测试网络环境检测
 */
async function testNetworkEnvironmentDetection(networkService: NetworkService) {
  console.log('检测当前网络环境...');
  
  try {
    const networkInfo = await networkService.getNetworkInfo();
    console.log(`网络类型: ${networkInfo.type}`);
    console.log(`连接状态: ${networkInfo.isConnected ? '已连接' : '未连接'}`);
    console.log(`信号强度: ${networkInfo.signalStrength}%`);
    console.log(`运营商: ${networkInfo.carrier || networkInfo.operator || '未知'}`);
    console.log(`IP地址: ${networkInfo.ipAddress}`);
    console.log(`计费网络: ${networkInfo.isMetered ? '是' : '否'}`);

    const networkQuality = await networkService.getNetworkQuality();
    console.log(`网络质量: ${networkQuality}`);

    const isAvailable = await networkService.isNetworkAvailable();
    console.log(`网络可用性: ${isAvailable ? '可用' : '不可用'}`);

    console.log('✅ 网络环境检测完成');
  } catch (error) {
    console.warn('⚠️ 网络环境检测失败:', error.message);
  }
}

/**
 * 测试智能服务器选择
 */
async function testIntelligentServerSelection(serverSelectionService: ServerSelectionService) {
  console.log('执行智能服务器选择...');
  
  try {
    const startTime = Date.now();
    const bestServer = await serverSelectionService.selectBestServer();
    const selectionTime = Date.now() - startTime;

    console.log(`最佳服务器: ${bestServer.name}`);
    console.log(`服务器位置: ${bestServer.location}`);
    console.log(`服务器URL: ${bestServer.url}`);
    console.log(`选择耗时: ${selectionTime}ms`);

    // 获取服务器性能指标
    const metrics = serverSelectionService.getAllServerMetrics();
    console.log(`\n服务器性能指标 (共${metrics.length}个服务器):`);
    
    metrics.forEach(metric => {
      const health = serverSelectionService.getServerHealth(metric.serverId);
      console.log(`- ${metric.serverId}: 延迟=${metric.latency.toFixed(1)}ms, 稳定性=${metric.stability.toFixed(1)}, 成功率=${(metric.successRate*100).toFixed(1)}%, 健康=${health?.isHealthy ? '是' : '否'}`);
    });

    // 测试缓存效果
    const cacheStartTime = Date.now();
    const cachedServer = await serverSelectionService.selectBestServer();
    const cacheTime = Date.now() - cacheStartTime;

    console.log(`\n缓存测试: 缓存命中耗时=${cacheTime}ms (提升${((selectionTime-cacheTime)/selectionTime*100).toFixed(1)}%)`);
    console.log(`缓存一致性: ${bestServer.id === cachedServer.id ? '✅ 一致' : '⚠️ 不一致'}`);

    console.log('✅ 智能服务器选择验证完成');
  } catch (error) {
    console.warn('⚠️ 智能服务器选择失败:', error.message);
  }
}

/**
 * 测试自适应并发测试
 */
async function testAdaptiveConcurrentTesting(concurrentTestService: ConcurrentTestService, networkService: NetworkService) {
  console.log('执行自适应并发测试...');
  
  try {
    const networkInfo = await networkService.getNetworkInfo();
    const networkType = mapNetworkType(networkInfo.type);
    
    console.log(`当前网络类型: ${networkType}`);
    
    const testUrl = 'https://speed.cloudflare.com/__down?bytes=5000000';
    
    const result = await concurrentTestService.performConcurrentDownloadTest(
      testUrl,
      networkType,
      (progress: number, connectionId: number) => {
        if (progress % 20 === 0) { // 每20%记录一次
          console.log(`  连接${connectionId}进度: ${progress}%`);
        }
      }
    );

    console.log('\n并发测试结果:');
    console.log(`总下载速度: ${result.totalSpeed.toFixed(2)} Mbps`);
    console.log(`使用连接数: ${result.connectionResults.length}`);
    console.log(`成功连接数: ${result.successfulConnections}`);
    console.log(`总下载量: ${(result.totalBytesDownloaded/1024/1024).toFixed(2)} MB`);
    console.log(`总耗时: ${result.totalDuration.toFixed(2)} 秒`);
    console.log(`并发效率: ${(result.efficiency*100).toFixed(1)}%`);

    // 分析连接详情
    console.log('\n连接详情:');
    result.connectionResults.forEach(conn => {
      const status = conn.success ? '✅ 成功' : '❌ 失败';
      const speed = conn.success ? `${conn.averageSpeed.toFixed(2)}Mbps` : '0Mbps';
      const error = conn.error ? ` (${conn.error})` : '';
      console.log(`  连接${conn.connectionId}: ${status}, ${speed}, ${conn.duration.toFixed(2)}s${error}`);
    });

    // 评估并发优化效果
    const isOptimal = evaluateConcurrentOptimization(result, networkType);
    console.log(`\n并发优化评估: ${isOptimal ? '✅ 优化有效' : '⚠️ 可能需要调整'}`);

    console.log('✅ 自适应并发测试验证完成');
  } catch (error) {
    console.warn('⚠️ 自适应并发测试失败:', error.message);
  }
}

/**
 * 测试集成优化效果
 */
async function testIntegratedOptimizations(speedTestService: SpeedTestService) {
  console.log('测试集成优化效果...');
  
  try {
    // 执行完整的优化测试
    const result = await speedTestService.runFullTest((progress: TestProgress) => {
      console.log(`  ${progress.phase}阶段: ${progress.progress}%`);
    });

    console.log('\n集成优化测试结果:');
    console.log(`下载速度: ${result.downloadSpeed.toFixed(2)} Mbps`);
    console.log(`上传速度: ${result.uploadSpeed.toFixed(2)} Mbps`);
    console.log(`延迟: ${result.latency} ms`);
    console.log(`抖动: ${result.jitter.toFixed(2)} ms`);
    console.log(`丢包率: ${result.packetLoss.toFixed(2)}%`);

    // 获取服务器选择统计
    const serverStats = await speedTestService.getServerSelectionStats();
    if (serverStats) {
      console.log('\n服务器选择统计:');
      console.log(`总服务器数: ${serverStats.totalServers}`);
      console.log(`健康服务器数: ${serverStats.healthyServers}`);
      console.log(`平均延迟: ${serverStats.averageLatency.toFixed(1)} ms`);
      console.log(`最后更新: ${new Date(serverStats.lastUpdateTime).toLocaleString()}`);
    }

    // 评估整体质量
    if (result.qualityScore) {
      console.log(`\n测试质量评分: ${result.qualityScore}/100`);
    }

    if (result.reliability) {
      console.log(`整体可信度: ${result.reliability.overallScore}/100`);
      if (result.reliability.issues.length > 0) {
        console.log('发现的问题:');
        result.reliability.issues.forEach(issue => console.log(`  - ${issue}`));
      }
    }

    console.log('✅ 集成优化效果验证完成');
  } catch (error) {
    console.warn('⚠️ 集成优化测试失败:', error.message);
  }
}

/**
 * 执行高级性能分析
 */
async function performAdvancedPerformanceAnalysis(speedTestService: SpeedTestService) {
  console.log('执行高级性能分析...');
  
  const testRounds = 3;
  const results = [];

  for (let round = 1; round <= testRounds; round++) {
    console.log(`\n性能测试轮次 ${round}/${testRounds}`);
    
    try {
      const startTime = Date.now();
      const result = await speedTestService.runFullTest((progress: TestProgress) => {
        // 只记录关键进度点
        if (progress.progress % 50 === 0) {
          console.log(`    ${progress.phase}: ${progress.progress}%`);
        }
      });
      const duration = Date.now() - startTime;

      results.push({
        round,
        downloadSpeed: result.downloadSpeed,
        uploadSpeed: result.uploadSpeed,
        latency: result.latency,
        jitter: result.jitter,
        duration,
        qualityScore: result.qualityScore || 0
      });

      console.log(`  轮次${round}结果: 下载=${result.downloadSpeed.toFixed(2)}Mbps, 延迟=${result.latency}ms, 耗时=${duration}ms`);

      // 轮次间延迟
      if (round < testRounds) {
        await new Promise(resolve => setTimeout(resolve, 3000));
      }

    } catch (error) {
      console.warn(`  轮次${round}失败:`, error.message);
    }
  }

  // 分析结果
  if (results.length > 0) {
    console.log('\n=== 性能分析报告 ===');
    
    const avgDownload = results.reduce((sum, r) => sum + r.downloadSpeed, 0) / results.length;
    const avgUpload = results.reduce((sum, r) => sum + r.uploadSpeed, 0) / results.length;
    const avgLatency = results.reduce((sum, r) => sum + r.latency, 0) / results.length;
    const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    
    console.log(`平均下载速度: ${avgDownload.toFixed(2)} Mbps`);
    console.log(`平均上传速度: ${avgUpload.toFixed(2)} Mbps`);
    console.log(`平均延迟: ${avgLatency.toFixed(1)} ms`);
    console.log(`平均测试时间: ${avgDuration.toFixed(0)} ms`);

    // 计算稳定性
    const downloadStdDev = Math.sqrt(results.reduce((sum, r) => sum + Math.pow(r.downloadSpeed - avgDownload, 2), 0) / results.length);
    const latencyStdDev = Math.sqrt(results.reduce((sum, r) => sum + Math.pow(r.latency - avgLatency, 2), 0) / results.length);
    
    console.log(`下载速度稳定性: ${(downloadStdDev/avgDownload*100).toFixed(1)}% 变异系数`);
    console.log(`延迟稳定性: ${(latencyStdDev/avgLatency*100).toFixed(1)}% 变异系数`);

    const isStable = (downloadStdDev/avgDownload < 0.2) && (latencyStdDev/avgLatency < 0.3);
    console.log(`整体稳定性: ${isStable ? '✅ 稳定' : '⚠️ 波动较大'}`);
  }

  console.log('✅ 高级性能分析完成');
}

/**
 * 测试稳定性和可靠性
 */
async function testStabilityAndReliability(speedTestService: SpeedTestService) {
  console.log('测试稳定性和可靠性...');
  
  const quickTests = 5;
  const successCount = { value: 0 };
  const latencies = [];
  const speeds = [];

  for (let i = 1; i <= quickTests; i++) {
    try {
      console.log(`快速测试 ${i}/${quickTests}`);
      
      const latency = await speedTestService.testLatency();
      const speed = await speedTestService.testDownloadSpeed();
      
      latencies.push(latency);
      speeds.push(speed);
      successCount.value++;
      
      console.log(`  延迟: ${latency}ms, 速度: ${speed.toFixed(2)}Mbps`);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.warn(`  测试${i}失败:`, error.message);
    }
  }

  console.log('\n稳定性和可靠性分析:');
  console.log(`成功率: ${(successCount.value/quickTests*100).toFixed(1)}%`);
  
  if (latencies.length > 0) {
    const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    const latencyRange = Math.max(...latencies) - Math.min(...latencies);
    console.log(`延迟稳定性: 平均${avgLatency.toFixed(1)}ms, 范围${latencyRange.toFixed(1)}ms`);
  }
  
  if (speeds.length > 0) {
    const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
    const speedRange = Math.max(...speeds) - Math.min(...speeds);
    console.log(`速度稳定性: 平均${avgSpeed.toFixed(2)}Mbps, 范围${speedRange.toFixed(2)}Mbps`);
  }

  const isReliable = successCount.value >= quickTests * 0.8;
  console.log(`可靠性评估: ${isReliable ? '✅ 可靠' : '⚠️ 需要改进'}`);

  console.log('✅ 稳定性和可靠性测试完成');
}

/**
 * 测试资源使用效率
 */
async function testResourceEfficiency(concurrentTestService: ConcurrentTestService) {
  console.log('测试资源使用效率...');
  
  try {
    const testUrl = 'https://speed.cloudflare.com/__down?bytes=2000000'; // 2MB测试
    
    // 测试不同网络类型的资源使用
    const networkTypes = [NetworkType.WIFI, NetworkType.CELLULAR_4G, NetworkType.CELLULAR_5G];
    
    for (const networkType of networkTypes) {
      console.log(`\n测试${NetworkType[networkType]}网络的资源效率...`);
      
      const startTime = Date.now();
      const result = await concurrentTestService.performConcurrentDownloadTest(testUrl, networkType);
      const duration = Date.now() - startTime;
      
      const efficiency = result.efficiency;
      const connectionsUsed = result.connectionResults.length;
      const dataPerConnection = result.totalBytesDownloaded / connectionsUsed;
      
      console.log(`  连接数: ${connectionsUsed}`);
      console.log(`  并发效率: ${(efficiency*100).toFixed(1)}%`);
      console.log(`  每连接数据量: ${(dataPerConnection/1024/1024).toFixed(2)}MB`);
      console.log(`  总耗时: ${duration}ms`);
      console.log(`  资源效率: ${(result.totalSpeed/connectionsUsed).toFixed(2)}Mbps/连接`);
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log('✅ 资源使用效率测试完成');
  } catch (error) {
    console.warn('⚠️ 资源使用效率测试失败:', error.message);
  }
}

/**
 * 映射网络类型
 */
function mapNetworkType(networkTypeStr: string): NetworkType {
  const typeStr = networkTypeStr.toUpperCase();
  
  if (typeStr.includes('WIFI')) {
    return NetworkType.WIFI;
  } else if (typeStr.includes('5G')) {
    return NetworkType.CELLULAR_5G;
  } else if (typeStr.includes('4G') || typeStr.includes('LTE')) {
    return NetworkType.CELLULAR_4G;
  } else if (typeStr.includes('3G')) {
    return NetworkType.CELLULAR_3G;
  } else if (typeStr.includes('ETHERNET')) {
    return NetworkType.ETHERNET;
  } else {
    return NetworkType.UNKNOWN;
  }
}

/**
 * 评估并发优化效果
 */
function evaluateConcurrentOptimization(result: any, networkType: NetworkType): boolean {
  // 基于网络类型和结果评估优化效果
  if (networkType === NetworkType.WIFI || networkType === NetworkType.ETHERNET) {
    // WiFi和以太网应该使用多连接且效率较高
    return result.connectionResults.length > 1 && result.efficiency > 0.6;
  } else if (networkType === NetworkType.CELLULAR_5G) {
    // 5G可以使用多连接
    return result.successfulConnections > 0 && result.efficiency > 0.5;
  } else {
    // 其他移动网络主要看成功率
    return result.successfulConnections > 0 && result.totalSpeed > 0;
  }
}

// 运行高级优化验证
main().catch(error => {
  console.error('高级优化验证脚本执行失败:', error);
});
