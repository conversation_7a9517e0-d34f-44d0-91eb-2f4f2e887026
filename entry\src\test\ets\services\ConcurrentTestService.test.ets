// 自适应并发测试服务单元测试
// 验证并发测试的准确性和资源管理

import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';
import { ConcurrentTestService } from '../../../main/ets/services/ConcurrentTestService';
import { NetworkType } from '../../../main/ets/common/types/SpeedTestModels';

export default function concurrentTestServiceTest() {
  describe('ConcurrentTestService', function () {
    let concurrentTestService: ConcurrentTestService;
    const testUrl = 'https://speed.cloudflare.com/__down?bytes=5000000'; // 5MB测试文件

    beforeEach(function () {
      concurrentTestService = new ConcurrentTestService();
    });

    afterEach(function () {
      concurrentTestService.stopTest();
    });

    /**
     * WiFi网络并发测试
     */
    describe('WiFi网络并发测试', function () {
      it('应该能够在WiFi环境下执行并发下载测试', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI,
          (progress: number, connectionId: number) => {
            console.log(`WiFi测试 - 连接${connectionId}: ${progress.toFixed(1)}%`);
          }
        );

        // 验证测试结果
        expect(result).assertNotNull();
        expect(result.totalSpeed).assertGreaterThan(0);
        expect(result.connectionResults.length).assertGreaterThan(0);
        expect(result.successfulConnections).assertGreaterThan(0);
        expect(result.totalBytesDownloaded).assertGreaterThan(0);
        expect(result.efficiency).assertGreaterOrEqual(0);
        expect(result.efficiency).assertLessOrEqual(1);

        console.log(`WiFi并发测试结果: ${result.totalSpeed.toFixed(2)}Mbps, 效率: ${(result.efficiency*100).toFixed(1)}%`);
      });

      it('WiFi环境应该使用多个并发连接', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI
        );

        expect(result.connectionResults.length).assertGreaterThan(1);
        console.log(`WiFi并发连接数: ${result.connectionResults.length}`);
      });
    });

    /**
     * 移动网络并发测试
     */
    describe('移动网络并发测试', function () {
      it('应该能够在4G环境下执行测试', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.CELLULAR_4G,
          (progress: number, connectionId: number) => {
            console.log(`4G测试 - 连接${connectionId}: ${progress.toFixed(1)}%`);
          }
        );

        expect(result).assertNotNull();
        expect(result.totalSpeed).assertGreaterThan(0);
        expect(result.successfulConnections).assertGreaterThan(0);

        console.log(`4G并发测试结果: ${result.totalSpeed.toFixed(2)}Mbps`);
      });

      it('4G环境应该使用单个连接', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.CELLULAR_4G
        );

        expect(result.connectionResults.length).assertEqual(1);
        console.log(`4G连接数: ${result.connectionResults.length}`);
      });

      it('应该能够在5G环境下执行测试', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.CELLULAR_5G
        );

        expect(result).assertNotNull();
        expect(result.totalSpeed).assertGreaterThan(0);
        expect(result.connectionResults.length).assertGreaterOrEqual(1);

        console.log(`5G并发测试结果: ${result.totalSpeed.toFixed(2)}Mbps, 连接数: ${result.connectionResults.length}`);
      });
    });

    /**
     * 以太网并发测试
     */
    describe('以太网并发测试', function () {
      it('应该能够在以太网环境下执行并发测试', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.ETHERNET
        );

        expect(result).assertNotNull();
        expect(result.totalSpeed).assertGreaterThan(0);
        expect(result.connectionResults.length).assertGreaterThan(1); // 以太网应该支持多连接

        console.log(`以太网并发测试结果: ${result.totalSpeed.toFixed(2)}Mbps, 连接数: ${result.connectionResults.length}`);
      });
    });

    /**
     * 并发效率测试
     */
    describe('并发效率测试', function () {
      it('应该正确计算并发效率', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI
        );

        expect(result.efficiency).assertGreaterOrEqual(0);
        expect(result.efficiency).assertLessOrEqual(1);

        // 在理想情况下，并发效率应该相对较高
        if (result.successfulConnections > 1) {
          expect(result.efficiency).assertGreaterThan(0.3); // 至少30%效率
        }

        console.log(`并发效率: ${(result.efficiency*100).toFixed(1)}%`);
      });

      it('应该正确聚合多连接结果', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI
        );

        // 验证聚合逻辑
        const successfulResults = result.connectionResults.filter(r => r.success);
        expect(result.successfulConnections).assertEqual(successfulResults.length);

        const totalBytes = result.connectionResults.reduce((sum, r) => sum + r.bytesDownloaded, 0);
        expect(result.totalBytesDownloaded).assertEqual(totalBytes);

        console.log(`聚合结果验证: 成功连接${result.successfulConnections}个, 总下载${(result.totalBytesDownloaded/1024/1024).toFixed(2)}MB`);
      });
    });

    /**
     * 错误处理测试
     */
    describe('错误处理测试', function () {
      it('应该正确处理单个连接失败', async function () {
        // 使用一个可能失败的URL进行测试
        const unreliableUrl = 'https://httpstat.us/500?bytes=1000000';
        
        try {
          const result = await concurrentTestService.performConcurrentDownloadTest(
            unreliableUrl,
            NetworkType.WIFI
          );

          // 即使有连接失败，也应该返回结果
          expect(result).assertNotNull();
          expect(result.connectionResults.length).assertGreaterThan(0);

          // 检查是否正确记录了失败的连接
          const failedConnections = result.connectionResults.filter(r => !r.success);
          if (failedConnections.length > 0) {
            failedConnections.forEach(conn => {
              expect(conn.error).assertNotNull();
              expect(typeof conn.error).assertEqual('string');
            });
          }

          console.log(`错误处理测试: 成功${result.successfulConnections}个, 失败${failedConnections.length}个连接`);
        } catch (error) {
          // 如果所有连接都失败，应该抛出有意义的错误
          expect(error.message).assertNotNull();
          console.log(`所有连接失败，错误信息: ${error.message}`);
        }
      });

      it('应该能够停止正在进行的测试', async function () {
        // 启动一个长时间的测试
        const longTestPromise = concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI
        );

        // 短暂延迟后停止测试
        setTimeout(() => {
          concurrentTestService.stopTest();
        }, 1000);

        try {
          const result = await longTestPromise;
          // 测试可能在停止前完成，这也是正常的
          expect(result).assertNotNull();
        } catch (error) {
          // 或者测试被中断，这也是预期的
          console.log(`测试被停止: ${error.message}`);
        }
      });
    });

    /**
     * 资源管理测试
     */
    describe('资源管理测试', function () {
      it('应该在资源限制下降级测试', async function () {
        // 这个测试比较难模拟，因为需要模拟资源限制
        // 在实际实现中，可以通过依赖注入来模拟资源状态
        
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI
        );

        expect(result).assertNotNull();
        expect(result.totalSpeed).assertGreaterThan(0);

        console.log(`资源管理测试完成: ${result.totalSpeed.toFixed(2)}Mbps`);
      });
    });

    /**
     * 性能测试
     */
    describe('性能测试', function () {
      it('并发测试应该在合理时间内完成', async function () {
        const startTime = Date.now();
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI
        );
        const duration = Date.now() - startTime;

        expect(result).assertNotNull();
        expect(duration).assertLessThan(60000); // 60秒内完成

        console.log(`并发测试耗时: ${duration}ms, 速度: ${result.totalSpeed.toFixed(2)}Mbps`);
      });

      it('并发测试应该比单连接测试更快（在支持的网络上）', async function () {
        // 这个测试需要比较并发和单连接的性能
        // 实际实现中可能需要更复杂的测试设置
        
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI
        );

        expect(result).assertNotNull();
        
        if (result.connectionResults.length > 1 && result.efficiency > 0.5) {
          // 如果使用了多连接且效率较高，说明并发优化有效
          console.log(`并发优化有效: ${result.connectionResults.length}个连接, 效率${(result.efficiency*100).toFixed(1)}%`);
        }
      });
    });

    /**
     * 网络类型适配测试
     */
    describe('网络类型适配测试', function () {
      it('应该根据网络类型调整并发策略', async function () {
        const networkTypes = [
          NetworkType.WIFI,
          NetworkType.CELLULAR_5G,
          NetworkType.CELLULAR_4G,
          NetworkType.CELLULAR_3G,
          NetworkType.ETHERNET
        ];

        for (const networkType of networkTypes) {
          try {
            const result = await concurrentTestService.performConcurrentDownloadTest(
              testUrl,
              networkType
            );

            expect(result).assertNotNull();
            expect(result.totalSpeed).assertGreaterThan(0);

            console.log(`${NetworkType[networkType]}网络测试: ${result.totalSpeed.toFixed(2)}Mbps, ${result.connectionResults.length}个连接`);

            // 短暂延迟避免过快请求
            await new Promise(resolve => setTimeout(resolve, 1000));
          } catch (error) {
            console.warn(`${NetworkType[networkType]}网络测试失败:`, error.message);
          }
        }
      });
    });

    /**
     * 集成测试
     */
    describe('集成测试', function () {
      it('应该能够完成完整的并发测试流程', async function () {
        const result = await concurrentTestService.performConcurrentDownloadTest(
          testUrl,
          NetworkType.WIFI,
          (progress: number, connectionId: number) => {
            if (progress % 25 === 0) { // 只记录25%的倍数进度
              console.log(`集成测试进度 - 连接${connectionId}: ${progress}%`);
            }
          }
        );

        // 验证完整结果
        expect(result).assertNotNull();
        expect(result.totalSpeed).assertGreaterThan(0);
        expect(result.connectionResults.length).assertGreaterThan(0);
        expect(result.successfulConnections).assertGreaterThan(0);
        expect(result.totalBytesDownloaded).assertGreaterThan(0);
        expect(result.totalDuration).assertGreaterThan(0);
        expect(result.efficiency).assertGreaterOrEqual(0);
        expect(result.efficiency).assertLessOrEqual(1);

        // 验证连接结果详情
        result.connectionResults.forEach((connResult, index) => {
          expect(connResult.connectionId).assertEqual(index);
          expect(typeof connResult.success).assertEqual('boolean');
          expect(connResult.bytesDownloaded).assertGreaterOrEqual(0);
          expect(connResult.duration).assertGreaterOrEqual(0);
          expect(connResult.averageSpeed).assertGreaterOrEqual(0);
          
          if (!connResult.success) {
            expect(connResult.error).assertNotNull();
          }
        });

        console.log('=== 集成测试完整结果 ===');
        console.log(`总速度: ${result.totalSpeed.toFixed(2)} Mbps`);
        console.log(`成功连接: ${result.successfulConnections}/${result.connectionResults.length}`);
        console.log(`总下载量: ${(result.totalBytesDownloaded/1024/1024).toFixed(2)} MB`);
        console.log(`总耗时: ${result.totalDuration.toFixed(2)} 秒`);
        console.log(`并发效率: ${(result.efficiency*100).toFixed(1)}%`);
      });
    });
  });
}
