// LazyForEach数据源实现
// 用于优化大量数据的渲染性能

import { SpeedTestResult } from '../types/SpeedTestModels';

/**
 * 懒加载数据源类
 * 实现IDataSource接口，支持LazyForEach组件
 */
export class LazyDataSource implements IDataSource {
  private listeners: DataChangeListener[] = [];
  private originDataArray: SpeedTestResult[] = [];

  /**
   * 获取数据总数
   */
  totalCount(): number {
    return this.originDataArray.length;
  }

  /**
   * 获取指定索引的数据
   */
  getData(index: number): SpeedTestResult {
    if (index >= 0 && index < this.originDataArray.length) {
      return this.originDataArray[index];
    }
    // 返回默认数据，避免undefined
    return {
      id: '',
      timestamp: 0,
      downloadSpeed: 0,
      uploadSpeed: 0,
      latency: 0,
      jitter: 0,
      packetLoss: 0,
      networkInfo: {
        type: '',
        operator: '',
        ipAddress: '',
        signalStrength: 0,
        isConnected: false
      },
      testServer: {
        id: '',
        name: '',
        url: '',
        location: '',
        latency: 0
      },
      location: '',
      duration: 0
    };
  }

  /**
   * 注册数据变化监听器
   */
  registerDataChangeListener(listener: DataChangeListener): void {
    if (this.listeners.indexOf(listener) < 0) {
      this.listeners.push(listener);
    }
  }

  /**
   * 注销数据变化监听器
   */
  unregisterDataChangeListener(listener: DataChangeListener): void {
    const pos = this.listeners.indexOf(listener);
    if (pos >= 0) {
      this.listeners.splice(pos, 1);
    }
  }

  /**
   * 通知数据重新加载
   */
  notifyDataReload(): void {
    this.listeners.forEach(listener => {
      listener.onDataReloaded();
    });
  }

  /**
   * 通知数据添加
   */
  notifyDataAdd(index: number): void {
    this.listeners.forEach(listener => {
      listener.onDataAdd(index);
    });
  }

  /**
   * 通知数据变化
   */
  notifyDataChange(index: number): void {
    this.listeners.forEach(listener => {
      listener.onDataChange(index);
    });
  }

  /**
   * 通知数据删除
   */
  notifyDataDelete(index: number): void {
    this.listeners.forEach(listener => {
      listener.onDataDelete(index);
    });
  }

  /**
   * 通知数据移动
   */
  notifyDataMove(from: number, to: number): void {
    this.listeners.forEach(listener => {
      listener.onDataMove(from, to);
    });
  }

  /**
   * 设置数据源
   */
  setDataArray(dataArray: SpeedTestResult[]): void {
    this.originDataArray = [...dataArray];
    this.notifyDataReload();
  }

  /**
   * 添加数据
   */
  addData(index: number, data: SpeedTestResult): void {
    this.originDataArray.splice(index, 0, data);
    this.notifyDataAdd(index);
  }

  /**
   * 在末尾添加数据
   */
  pushData(data: SpeedTestResult): void {
    this.originDataArray.push(data);
    this.notifyDataAdd(this.originDataArray.length - 1);
  }

  /**
   * 删除数据
   */
  deleteData(index: number): void {
    if (index >= 0 && index < this.originDataArray.length) {
      this.originDataArray.splice(index, 1);
      this.notifyDataDelete(index);
    }
  }

  /**
   * 清空所有数据
   */
  clearData(): void {
    this.originDataArray = [];
    this.notifyDataReload();
  }

  /**
   * 获取原始数据数组（只读）
   */
  getDataArray(): readonly SpeedTestResult[] {
    return this.originDataArray;
  }

  /**
   * 根据条件筛选数据
   */
  filterData(predicate: (item: SpeedTestResult) => boolean): void {
    this.originDataArray = this.originDataArray.filter(predicate);
    this.notifyDataReload();
  }

  /**
   * 排序数据
   */
  sortData(compareFn: (a: SpeedTestResult, b: SpeedTestResult) => number): void {
    this.originDataArray.sort(compareFn);
    this.notifyDataReload();
  }

  /**
   * 查找数据索引
   */
  findIndex(predicate: (item: SpeedTestResult) => boolean): number {
    return this.originDataArray.findIndex(predicate);
  }

  /**
   * 更新指定索引的数据
   */
  updateData(index: number, data: SpeedTestResult): void {
    if (index >= 0 && index < this.originDataArray.length) {
      this.originDataArray[index] = data;
      this.notifyDataChange(index);
    }
  }
}

/**
 * 分页数据源类
 * 支持分页加载，进一步优化性能
 */
export class PaginatedDataSource extends LazyDataSource {
  private pageSize: number = 20;
  private currentPage: number = 0;
  private allData: SpeedTestResult[] = [];
  private isLoading: boolean = false;

  constructor(pageSize: number = 20) {
    super();
    this.pageSize = pageSize;
  }

  /**
   * 设置所有数据
   */
  setAllData(data: SpeedTestResult[]): void {
    this.allData = [...data];
    this.currentPage = 0;
    this.loadPage(0);
  }

  /**
   * 加载指定页面
   */
  private loadPage(page: number): void {
    const startIndex = page * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.allData.length);
    const pageData = this.allData.slice(0, endIndex);
    super.setDataArray(pageData);
  }

  /**
   * 加载更多数据
   */
  loadMore(): boolean {
    if (this.isLoading) {
      return false;
    }

    const totalPages = Math.ceil(this.allData.length / this.pageSize);
    if (this.currentPage >= totalPages - 1) {
      return false; // 没有更多数据
    }

    this.isLoading = true;
    this.currentPage++;
    
    // 模拟异步加载
    setTimeout(() => {
      this.loadPage(this.currentPage);
      this.isLoading = false;
    }, 100);

    return true;
  }

  /**
   * 是否还有更多数据
   */
  hasMore(): boolean {
    const totalPages = Math.ceil(this.allData.length / this.pageSize);
    return this.currentPage < totalPages - 1;
  }

  /**
   * 是否正在加载
   */
  isLoadingData(): boolean {
    return this.isLoading;
  }

  /**
   * 重置到第一页
   */
  reset(): void {
    this.currentPage = 0;
    this.isLoading = false;
    this.loadPage(0);
  }
}
