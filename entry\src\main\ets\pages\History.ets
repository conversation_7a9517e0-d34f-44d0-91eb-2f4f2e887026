// 历史记录页面
// 显示测试历史数据

import { StorageService } from '../services/StorageService';
import { NetworkService } from '../services/NetworkService';
import { SpeedTestResult } from '../common/types/SpeedTestModels';
import { TypeConverter } from '../common/utils/TypeConverter';
import { DateUtil } from '../common/utils/DateUtil';
import { LazyDataSource, PaginatedDataSource } from '../common/utils/LazyDataSource';
import router from '@ohos.router';
import { promptAction } from '@kit.ArkUI';
import { common } from '@kit.AbilityKit';

/**
 * 统计信息接口
 */
interface TestStatistics {
  totalTests: number;
  averageDownloadSpeed: number;
  averageUploadSpeed: number;
  averageLatency: number;
  bestDownloadSpeed: number;
  bestUploadSpeed: number;
  bestLatency: number;
}

@Entry
@Component
struct History {
  @State testHistory: SpeedTestResult[] = [];
  @State filteredHistory: SpeedTestResult[] = [];
  @State selectedFilter: string = 'all'; // all, today, week, month
  @State isLoading: boolean = true;
  @State statistics: TestStatistics | null = null;
  
  private storageService = new StorageService();
  private networkService = new NetworkService();
  private dataSource = new PaginatedDataSource(20); // 每页20条数据
  
  async aboutToAppear() {
    await this.loadHistoryData();
  }
  
  async loadHistoryData() {
    this.isLoading = true;

    try {
      // 获取应用上下文并初始化存储服务
      const uiContext = this.getUIContext();
      const context = uiContext.getHostContext() as common.UIAbilityContext;
      await this.storageService.initialize(context);
      this.testHistory = await this.storageService.getTestHistory();
      const stats = await this.storageService.getTestStatistics();
      this.statistics = {
        totalTests: stats.totalTests,
        averageDownloadSpeed: stats.averageDownloadSpeed,
        averageUploadSpeed: stats.averageUploadSpeed,
        averageLatency: stats.averageLatency,
        bestDownloadSpeed: stats.bestDownloadSpeed,
        bestUploadSpeed: stats.bestUploadSpeed,
        bestLatency: stats.bestLatency
      };

      // 初始化数据源
      this.dataSource.setAllData(this.testHistory);
      this.applyFilter(this.selectedFilter);
    } catch (error) {
      console.error('加载历史数据失败:', error);
    } finally {
      this.isLoading = false;
    }
  }
  
  async applyFilter(filter: string) {
    this.selectedFilter = filter;

    try {
      let filteredData: SpeedTestResult[] = [];
      switch (filter) {
        case 'today':
          filteredData = await this.storageService.getTodayTestHistory();
          break;
        case 'week':
          filteredData = await this.storageService.getThisWeekTestHistory();
          break;
        case 'month':
          filteredData = await this.storageService.getThisMonthTestHistory();
          break;
        default:
          filteredData = this.testHistory;
          break;
      }

      // 更新数据源
      this.dataSource.setAllData(filteredData);
    } catch (error) {
      console.error('筛选历史数据失败:', error);
      this.dataSource.setAllData([]);
    }
  }
  
  async deleteTestResult(resultId: string) {
    try {
      await this.storageService.deleteTestResult(resultId);
      await this.loadHistoryData();
    } catch (error) {
      console.error('删除测试记录失败:', error);
    }
  }
  
  async clearAllHistory() {
    try {
      await this.storageService.clearAllTestHistory();
      await this.loadHistoryData();
    } catch (error) {
      console.error('清空历史记录失败:', error);
    }
  }
  
  navigateBack() {
    router.back();
  }
  
  getNetworkQualityColor(downloadSpeed: number, uploadSpeed: number, latency: number): string {
    const score = this.networkService.getNetworkQualityScore(downloadSpeed, uploadSpeed, latency);
    if (score >= 90) {
      return '#34C759';
    } else if (score >= 75) {
      return '#007AFF';
    } else if (score >= 60) {
      return '#FF9500';
    } else {
      return '#FF3B30';
    }
  }
  
  build() {
    Column() {
      // 标题栏
      Row() {
        Button() {
          Text('← 返回')
            .fontSize(16)
            .fontColor('#007AFF')
        }
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.navigateBack();
        })
        
        Text('测试历史')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
        
        Button() {
          Text('清空')
            .fontSize(16)
            .fontColor('#FF3B30')
        }
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          promptAction.showDialog({
            title: '确认清空',
            message: '确定要清空所有测试历史记录吗？此操作不可恢复。',
            buttons: [
              {
                text: '取消',
                color: '#007AFF'
              },
              {
                text: '确定',
                color: '#FF3B30'
              }
            ]
          }).then((data: promptAction.ShowDialogSuccessResponse) => {
            if (data.index === 1) {
              this.clearAllHistory();
            }
          });
        })
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 16, bottom: 16 })
      .justifyContent(FlexAlign.SpaceBetween)
      
      // 统计信息
      if (this.statistics && this.statistics.totalTests > 0) {
        Column() {
          Text('统计信息')
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .fontColor('#333333')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 12 })
          
          Row() {
            Column() {
              Text(`${this.statistics.totalTests}`)
                .fontSize(24)
                .fontWeight(FontWeight.Bold)
                .fontColor('#007AFF')
              Text('总测试次数')
                .fontSize(12)
                .fontColor('#666666')
                .margin({ top: 4 })
            }
            .layoutWeight(1)
            
            Column() {
              Text(TypeConverter.formatSpeed(this.statistics.averageDownloadSpeed))
                .fontSize(20)
                .fontWeight(FontWeight.Bold)
                .fontColor('#34C759')
              Text('平均下载')
                .fontSize(12)
                .fontColor('#666666')
                .margin({ top: 4 })
            }
            .layoutWeight(1)
            
            Column() {
              Text(TypeConverter.formatSpeed(this.statistics.averageUploadSpeed))
                .fontSize(20)
                .fontWeight(FontWeight.Bold)
                .fontColor('#FF9500')
              Text('平均上传')
                .fontSize(12)
                .fontColor('#666666')
                .margin({ top: 4 })
            }
            .layoutWeight(1)
            
            Column() {
              Text(TypeConverter.formatLatency(this.statistics.averageLatency))
                .fontSize(20)
                .fontWeight(FontWeight.Bold)
                .fontColor('#FF3B30')
              Text('平均延迟')
                .fontSize(12)
                .fontColor('#666666')
                .margin({ top: 4 })
            }
            .layoutWeight(1)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceAround)
        }
        .width('100%')
        .padding(16)
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ left: 16, right: 16, bottom: 16 })
      }
      
      // 筛选按钮
      Row() {
        Button('全部')
          .fontSize(14)
          .backgroundColor(this.selectedFilter === 'all' ? '#007AFF' : '#F2F2F7')
          .fontColor(this.selectedFilter === 'all' ? '#FFFFFF' : '#333333')
          .borderRadius(16)
          .padding({ left: 16, right: 16, top: 8, bottom: 8 })
          .onClick(() => {
            this.applyFilter('all');
          })
        
        Button('今日')
          .fontSize(14)
          .backgroundColor(this.selectedFilter === 'today' ? '#007AFF' : '#F2F2F7')
          .fontColor(this.selectedFilter === 'today' ? '#FFFFFF' : '#333333')
          .borderRadius(16)
          .padding({ left: 16, right: 16, top: 8, bottom: 8 })
          .margin({ left: 8 })
          .onClick(() => {
            this.applyFilter('today');
          })
        
        Button('本周')
          .fontSize(14)
          .backgroundColor(this.selectedFilter === 'week' ? '#007AFF' : '#F2F2F7')
          .fontColor(this.selectedFilter === 'week' ? '#FFFFFF' : '#333333')
          .borderRadius(16)
          .padding({ left: 16, right: 16, top: 8, bottom: 8 })
          .margin({ left: 8 })
          .onClick(() => {
            this.applyFilter('week');
          })
        
        Button('本月')
          .fontSize(14)
          .backgroundColor(this.selectedFilter === 'month' ? '#007AFF' : '#F2F2F7')
          .fontColor(this.selectedFilter === 'month' ? '#FFFFFF' : '#333333')
          .borderRadius(16)
          .padding({ left: 16, right: 16, top: 8, bottom: 8 })
          .margin({ left: 8 })
          .onClick(() => {
            this.applyFilter('month');
          })
      }
      .width('100%')
      .padding({ left: 16, right: 16, bottom: 16 })
      .justifyContent(FlexAlign.Start)
      
      // 历史记录列表
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#007AFF')
          Text('加载中...')
            .fontSize(14)
            .fontColor('#666666')
            .margin({ top: 12 })
        }
        .layoutWeight(1)
        .width('100%')
        .justifyContent(FlexAlign.Center)
      } else if (this.filteredHistory.length === 0) {
        Column() {
          Text('暂无测试记录')
            .fontSize(16)
            .fontColor('#999999')
          Text('开始第一次网速测试吧！')
            .fontSize(14)
            .fontColor('#CCCCCC')
            .margin({ top: 8 })
        }
        .layoutWeight(1)
        .width('100%')
        .justifyContent(FlexAlign.Center)
      } else {
        List({ space: 8 }) {
          LazyForEach(this.dataSource, (result: SpeedTestResult, index: number) => {
            ListItem() {
              Row() {
                Column() {
                  // 测试时间
                  Text(DateUtil.formatDateTime(result.timestamp))
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                    .fontColor('#333333')
                    .alignSelf(ItemAlign.Start)
                  
                  // 网络信息
                  Text(`${this.networkService.getNetworkTypeDescription(result.networkInfo.type)} | ${result.testServer.name}`)
                    .fontSize(12)
                    .fontColor('#999999')
                    .margin({ top: 4 })
                    .alignSelf(ItemAlign.Start)
                  
                  // 速度信息
                  Row() {
                    Text(`↓ ${TypeConverter.formatSpeed(result.downloadSpeed)}`)
                      .fontSize(14)
                      .fontColor('#34C759')
                      .fontWeight(FontWeight.Medium)
                    
                    Text(`↑ ${TypeConverter.formatSpeed(result.uploadSpeed)}`)
                      .fontSize(14)
                      .fontColor('#FF9500')
                      .fontWeight(FontWeight.Medium)
                      .margin({ left: 16 })
                    
                    Text(`${TypeConverter.formatLatency(result.latency)}`)
                      .fontSize(14)
                      .fontColor('#FF3B30')
                      .fontWeight(FontWeight.Medium)
                      .margin({ left: 16 })
                  }
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 8 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)
                
                // 质量评分
                Column() {
                  Text(`${this.networkService.getNetworkQualityScore(result.downloadSpeed, result.uploadSpeed, result.latency)}`)
                    .fontSize(20)
                    .fontWeight(FontWeight.Bold)
                    .fontColor(this.getNetworkQualityColor(result.downloadSpeed, result.uploadSpeed, result.latency))
                  Text('评分')
                    .fontSize(10)
                    .fontColor('#666666')
                    .margin({ top: 2 })
                }
                .alignItems(HorizontalAlign.Center)
                
                // 删除按钮
                Button() {
                  Text('×')
                    .fontSize(18)
                    .fontColor('#FF3B30')
                }
                .width(32)
                .height(32)
                .backgroundColor('#F2F2F7')
                .borderRadius(16)
                .margin({ left: 12 })
                .onClick(() => {
                  promptAction.showDialog({
                    title: '确认删除',
                    message: '确定要删除这条测试记录吗？',
                    buttons: [
                      {
                        text: '取消',
                        color: '#007AFF'
                      },
                      {
                        text: '删除',
                        color: '#FF3B30'
                      }
                    ]
                  }).then((data: promptAction.ShowDialogSuccessResponse) => {
                    if (data.index === 1) {
                      this.deleteTestResult(result.id);
                    }
                  });
                })
              }
              .width('100%')
              .padding(16)
              .backgroundColor('#FFFFFF')
              .borderRadius(12)
              .alignItems(VerticalAlign.Center)
            }
          }, (result: SpeedTestResult) => result.id)
        }
        .layoutWeight(1)
        .width('100%')
        .padding({ left: 16, right: 16 })
        .scrollBar(BarState.Auto)
        .onReachEnd(() => {
          // 上拉加载更多
          if (this.dataSource.hasMore() && !this.dataSource.isLoadingData()) {
            this.dataSource.loadMore();
          }
        })
      }
    }
    .height('100%')
    .width('100%')
    .backgroundColor('#F2F2F7')
  }
}