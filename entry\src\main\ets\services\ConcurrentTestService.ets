// 自适应并发测试服务
// 根据网络类型动态调整并发连接数，优化带宽利用率

import http from '@ohos.net.http';
import { NetworkType } from '../common/types/SpeedTestModels';

/**
 * 并发测试配置接口
 */
interface ConcurrentTestConfig {
  maxConnections: number;
  connectionTimeout: number;
  readTimeout: number;
  testDuration: number;
  fileSize: number;
}

/**
 * 并发连接结果接口
 */
interface ConnectionResult {
  connectionId: number;
  success: boolean;
  bytesDownloaded: number;
  duration: number;
  averageSpeed: number;
  error?: string;
}

/**
 * 并发测试结果接口
 */
interface ConcurrentTestResult {
  totalSpeed: number;
  connectionResults: ConnectionResult[];
  successfulConnections: number;
  totalBytesDownloaded: number;
  totalDuration: number;
  efficiency: number; // 并发效率 (0-1)
}

/**
 * 网络类型配置映射
 */
interface NetworkTypeConfig {
  [key: string]: ConcurrentTestConfig;
}

/**
 * 自适应并发测试服务类
 */
export class ConcurrentTestService {
  // 网络类型配置
  private readonly NETWORK_CONFIGS: NetworkTypeConfig = {
    'WIFI': {
      maxConnections: 4,
      connectionTimeout: 10000,
      readTimeout: 30000,
      testDuration: 15000,
      fileSize: 10 * 1024 * 1024 // 10MB per connection
    },
    'ETHERNET': {
      maxConnections: 6,
      connectionTimeout: 8000,
      readTimeout: 25000,
      testDuration: 12000,
      fileSize: 15 * 1024 * 1024 // 15MB per connection
    },
    'CELLULAR_5G': {
      maxConnections: 2,
      connectionTimeout: 15000,
      readTimeout: 35000,
      testDuration: 20000,
      fileSize: 8 * 1024 * 1024 // 8MB per connection
    },
    'CELLULAR_4G': {
      maxConnections: 1,
      connectionTimeout: 20000,
      readTimeout: 40000,
      testDuration: 25000,
      fileSize: 5 * 1024 * 1024 // 5MB per connection
    },
    'CELLULAR_3G': {
      maxConnections: 1,
      connectionTimeout: 25000,
      readTimeout: 45000,
      testDuration: 30000,
      fileSize: 2 * 1024 * 1024 // 2MB per connection
    },
    'UNKNOWN': {
      maxConnections: 1,
      connectionTimeout: 15000,
      readTimeout: 30000,
      testDuration: 20000,
      fileSize: 5 * 1024 * 1024 // 5MB per connection
    }
  };

  // 资源限制配置
  private readonly RESOURCE_LIMITS = {
    maxCpuUsage: 70,        // 最大CPU使用率 (%)
    maxMemoryUsage: 80,     // 最大内存使用率 (%)
    batteryThreshold: 20,   // 电池电量阈值 (%)
    thermalThreshold: 40    // 温度阈值 (°C)
  };

  private isTestRunning = false;
  private shouldStopTest = false;

  /**
   * 执行自适应并发下载测试
   */
  async performConcurrentDownloadTest(
    baseUrl: string, 
    networkType: NetworkType,
    progressCallback?: (progress: number, connectionId: number) => void
  ): Promise<ConcurrentTestResult> {
    
    console.log(`开始自适应并发测试，网络类型: ${networkType}`);
    
    // 获取网络配置
    const config = this.getConfigForNetworkType(networkType);
    console.log(`并发配置: ${config.maxConnections}个连接, 每连接${(config.fileSize/1024/1024).toFixed(1)}MB`);

    // 检查资源限制
    const resourceCheck = await this.checkResourceLimits();
    if (!resourceCheck.canProceed) {
      console.warn('资源限制检查失败:', resourceCheck.reason);
      // 降级到单连接测试
      config.maxConnections = 1;
    }

    this.isTestRunning = true;
    this.shouldStopTest = false;

    try {
      // 预热连接
      await this.warmupConnections(baseUrl, config.maxConnections);

      // 执行并发测试
      const result = await this.executeConcurrentTest(baseUrl, config, progressCallback);
      
      console.log(`并发测试完成: 总速度=${result.totalSpeed.toFixed(2)}Mbps, 效率=${(result.efficiency*100).toFixed(1)}%`);
      
      return result;

    } catch (error) {
      console.error('并发测试失败:', error);
      throw error;
    } finally {
      this.isTestRunning = false;
    }
  }

  /**
   * 根据网络类型获取配置
   */
  private getConfigForNetworkType(networkType: NetworkType): ConcurrentTestConfig {
    const typeStr = NetworkType[networkType] || 'UNKNOWN';
    return { ...this.NETWORK_CONFIGS[typeStr] } || { ...this.NETWORK_CONFIGS['UNKNOWN'] };
  }

  /**
   * 检查资源限制
   */
  private async checkResourceLimits(): Promise<{ canProceed: boolean; reason?: string }> {
    try {
      // 注意：HarmonyOS的系统API可能不同，这里是示例实现
      // 实际实现需要使用HarmonyOS提供的系统监控API
      
      // 检查电池电量（模拟）
      const batteryLevel = await this.getBatteryLevel();
      if (batteryLevel < this.RESOURCE_LIMITS.batteryThreshold) {
        return { 
          canProceed: false, 
          reason: `电池电量过低: ${batteryLevel}%` 
        };
      }

      // 检查内存使用率（模拟）
      const memoryUsage = await this.getMemoryUsage();
      if (memoryUsage > this.RESOURCE_LIMITS.maxMemoryUsage) {
        return { 
          canProceed: false, 
          reason: `内存使用率过高: ${memoryUsage}%` 
        };
      }

      // 检查设备温度（模拟）
      const temperature = await this.getDeviceTemperature();
      if (temperature > this.RESOURCE_LIMITS.thermalThreshold) {
        return { 
          canProceed: false, 
          reason: `设备温度过高: ${temperature}°C` 
        };
      }

      return { canProceed: true };

    } catch (error) {
      console.warn('资源检查失败，继续执行测试:', error);
      return { canProceed: true };
    }
  }

  /**
   * 预热连接
   */
  private async warmupConnections(baseUrl: string, connectionCount: number): Promise<void> {
    console.log(`预热 ${connectionCount} 个连接...`);
    
    const warmupPromises = [];
    for (let i = 0; i < connectionCount; i++) {
      warmupPromises.push(this.warmupSingleConnection(baseUrl, i));
    }

    try {
      await Promise.allSettled(warmupPromises);
      console.log('连接预热完成');
    } catch (error) {
      console.warn('连接预热部分失败:', error);
    }
  }

  /**
   * 预热单个连接
   */
  private async warmupSingleConnection(baseUrl: string, connectionId: number): Promise<void> {
    try {
      const warmupUrl = baseUrl.replace(/bytes=\d+/, 'bytes=1024'); // 1KB预热
      
      const httpRequest = http.createHttp();
      await httpRequest.request(warmupUrl, {
        method: http.RequestMethod.GET,
        expectDataType: http.HttpDataType.ARRAY_BUFFER,
        connectTimeout: 5000,
        readTimeout: 3000,
        header: {
          'Connection': 'keep-alive',
          'User-Agent': `HarmonyOS-SpeedTest-Conn${connectionId}/1.0.0`
        }
      });
      
      httpRequest.destroy();
    } catch (error) {
      console.warn(`连接 ${connectionId} 预热失败:`, error);
    }
  }

  /**
   * 执行并发测试
   */
  private async executeConcurrentTest(
    baseUrl: string,
    config: ConcurrentTestConfig,
    progressCallback?: (progress: number, connectionId: number) => void
  ): Promise<ConcurrentTestResult> {
    
    const testUrl = baseUrl.replace(/bytes=\d+/, `bytes=${config.fileSize}`);
    const connectionPromises: Promise<ConnectionResult>[] = [];

    // 启动所有并发连接
    for (let i = 0; i < config.maxConnections; i++) {
      const promise = this.executeConnectionTest(
        testUrl, 
        i, 
        config,
        progressCallback
      );
      connectionPromises.push(promise);
    }

    // 等待所有连接完成
    const results = await Promise.allSettled(connectionPromises);
    const connectionResults: ConnectionResult[] = [];

    // 处理结果
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        connectionResults.push(result.value);
      } else {
        console.warn(`连接 ${index} 失败:`, result.reason);
        connectionResults.push({
          connectionId: index,
          success: false,
          bytesDownloaded: 0,
          duration: 0,
          averageSpeed: 0,
          error: result.reason?.toString()
        });
      }
    });

    // 计算总体结果
    return this.aggregateResults(connectionResults);
  }

  /**
   * 执行单个连接测试
   */
  private async executeConnectionTest(
    testUrl: string,
    connectionId: number,
    config: ConcurrentTestConfig,
    progressCallback?: (progress: number, connectionId: number) => void
  ): Promise<ConnectionResult> {
    
    console.log(`连接 ${connectionId} 开始测试...`);
    
    const startTime = Date.now();
    let bytesDownloaded = 0;

    try {
      const httpRequest = http.createHttp();
      
      // 设置进度回调
      if (progressCallback) {
        httpRequest.on('dataReceive', (data) => {
          if (data instanceof ArrayBuffer) {
            bytesDownloaded += data.byteLength;
            const progress = Math.min(100, (bytesDownloaded / config.fileSize) * 100);
            progressCallback(progress, connectionId);
          }
        });
      }

      const response = await httpRequest.request(testUrl, {
        method: http.RequestMethod.GET,
        expectDataType: http.HttpDataType.ARRAY_BUFFER,
        connectTimeout: config.connectionTimeout,
        readTimeout: config.readTimeout,
        header: {
          'Connection': 'keep-alive',
          'User-Agent': `HarmonyOS-SpeedTest-Conn${connectionId}/1.0.0`,
          'Cache-Control': 'no-cache'
        }
      });

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      if (response.result && response.result instanceof ArrayBuffer) {
        bytesDownloaded = response.result.byteLength;
      }

      httpRequest.destroy();

      const averageSpeed = (bytesDownloaded * 8) / (duration * 1000000); // Mbps

      console.log(`连接 ${connectionId} 完成: ${(bytesDownloaded/1024/1024).toFixed(2)}MB, ${duration.toFixed(2)}s, ${averageSpeed.toFixed(2)}Mbps`);

      return {
        connectionId,
        success: true,
        bytesDownloaded,
        duration,
        averageSpeed,
      };

    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      console.warn(`连接 ${connectionId} 失败:`, (error as Error).message);

      return {
        connectionId,
        success: false,
        bytesDownloaded,
        duration,
        averageSpeed: 0,
        error: (error as Error).message
      };
    }
  }

  /**
   * 聚合并发测试结果
   */
  private aggregateResults(connectionResults: ConnectionResult[]): ConcurrentTestResult {
    const successfulResults = connectionResults.filter(r => r.success);
    const totalBytesDownloaded = connectionResults.reduce((sum, r) => sum + r.bytesDownloaded, 0);
    
    // 计算总体速度 - 使用重叠时间段的方法
    const maxDuration = Math.max(...connectionResults.map(r => r.duration));
    const totalSpeed = maxDuration > 0 ? (totalBytesDownloaded * 8) / (maxDuration * 1000000) : 0;

    // 计算并发效率
    const theoreticalSpeed = successfulResults.reduce((sum, r) => sum + r.averageSpeed, 0);
    const efficiency = theoreticalSpeed > 0 ? Math.min(1, totalSpeed / theoreticalSpeed) : 0;

    const result: ConcurrentTestResult = {
      totalSpeed,
      connectionResults,
      successfulConnections: successfulResults.length,
      totalBytesDownloaded,
      totalDuration: maxDuration,
      efficiency
    };

    // 记录详细日志
    console.log('并发测试聚合结果:');
    console.log(`- 成功连接: ${result.successfulConnections}/${connectionResults.length}`);
    console.log(`- 总下载量: ${(result.totalBytesDownloaded/1024/1024).toFixed(2)}MB`);
    console.log(`- 总耗时: ${result.totalDuration.toFixed(2)}s`);
    console.log(`- 总速度: ${result.totalSpeed.toFixed(2)}Mbps`);
    console.log(`- 并发效率: ${(result.efficiency*100).toFixed(1)}%`);

    return result;
  }

  /**
   * 停止并发测试
   */
  stopTest(): void {
    this.shouldStopTest = true;
    console.log('并发测试已停止');
  }

  /**
   * 获取电池电量（模拟实现）
   */
  private async getBatteryLevel(): Promise<number> {
    // 实际实现应该使用HarmonyOS的电池API
    // 这里返回模拟值
    return 80;
  }

  /**
   * 获取内存使用率（模拟实现）
   */
  private async getMemoryUsage(): Promise<number> {
    // 实际实现应该使用HarmonyOS的内存监控API
    // 这里返回模拟值
    return 60;
  }

  /**
   * 获取设备温度（模拟实现）
   */
  private async getDeviceTemperature(): Promise<number> {
    // 实际实现应该使用HarmonyOS的温度监控API
    // 这里返回模拟值
    return 35;
  }

  /**
   * 检查是否应该停止测试
   */
  private checkShouldStop(): boolean {
    return this.shouldStopTest;
  }
}
