// 自适应并发测试服务
// 根据网络类型动态调整并发连接数，优化带宽利用率

import http from '@ohos.net.http';
import { NetworkType } from '../common/types/SpeedTestModels';

/**
 * 并发测试配置接口
 */
interface ConcurrentTestConfig {
  maxConnections: number;
  connectionTimeout: number;
  readTimeout: number;
  testDuration: number;
  fileSize: number;
}

/**
 * 并发连接结果接口
 */
interface ConnectionResult {
  connectionId: number;
  success: boolean;
  bytesDownloaded: number;
  duration: number;
  averageSpeed: number;
  error?: string;
}

/**
 * 并发测试结果接口
 */
interface ConcurrentTestResult {
  totalSpeed: number;
  connectionResults: ConnectionResult[];
  successfulConnections: number;
  totalBytesDownloaded: number;
  totalDuration: number;
  efficiency: number; // 并发效率 (0-1)
}

/**
 * 网络类型配置映射
 */
interface NetworkTypeConfig {
  WIFI: ConcurrentTestConfig;
  ETHERNET: ConcurrentTestConfig;
  CELLULAR_5G: ConcurrentTestConfig;
  CELLULAR_4G: ConcurrentTestConfig;
  CELLULAR_3G: ConcurrentTestConfig;
  UNKNOWN: ConcurrentTestConfig;
}

/**
 * 资源限制接口
 */
interface ResourceLimits {
  maxCpuUsage: number;
  maxMemoryUsage: number;
  batteryThreshold: number;
  thermalThreshold: number;
}

/**
 * 资源检查结果接口
 */
interface ResourceCheckResult {
  canProceed: boolean;
  reason?: string;
}

/**
 * 实时速度信息接口
 */
interface RealTimeSpeedInfo {
  currentSpeed: number;    // 当前瞬时速度 (Mbps)
  averageSpeed: number;    // 平均速度 (Mbps)
  totalBytes: number;      // 总下载字节数
  elapsedTime: number;     // 已用时间 (ms)
  connectionId: number;    // 连接ID
  progress: number;        // 进度百分比
}

/**
 * 自适应并发测试服务类
 */
export class ConcurrentTestService {
  // 网络类型配置 - 针对千兆网络优化
  private readonly NETWORK_CONFIGS: NetworkTypeConfig = {
    'WIFI': {
      maxConnections: 16,  // 大幅增加并发连接数以充分利用千兆WiFi
      connectionTimeout: 8000,
      readTimeout: 45000,  // 增加读取超时以支持大文件
      testDuration: 30000, // 延长测试时间确保达到稳定速度
      fileSize: 50 * 1024 * 1024 // 50MB per connection，足够大以克服慢启动
    },
    'ETHERNET': {
      maxConnections: 20,  // 以太网通常更稳定，可以支持更多并发
      connectionTimeout: 6000,
      readTimeout: 40000,
      testDuration: 25000,
      fileSize: 60 * 1024 * 1024 // 60MB per connection，充分利用千兆带宽
    },
    'CELLULAR_5G': {
      maxConnections: 8,   // 5G网络增加并发数
      connectionTimeout: 12000,
      readTimeout: 35000,
      testDuration: 25000,
      fileSize: 30 * 1024 * 1024 // 30MB per connection
    },
    'CELLULAR_4G': {
      maxConnections: 4,   // 4G网络适度增加并发
      connectionTimeout: 15000,
      readTimeout: 40000,
      testDuration: 30000,
      fileSize: 20 * 1024 * 1024 // 20MB per connection
    },
    'CELLULAR_3G': {
      maxConnections: 2,   // 3G网络保持较低并发
      connectionTimeout: 20000,
      readTimeout: 45000,
      testDuration: 35000,
      fileSize: 10 * 1024 * 1024 // 10MB per connection
    },
    'UNKNOWN': {
      maxConnections: 8,   // 未知网络使用中等配置
      connectionTimeout: 12000,
      readTimeout: 35000,
      testDuration: 25000,
      fileSize: 25 * 1024 * 1024 // 25MB per connection
    }
  };

  // 资源限制配置
  private readonly RESOURCE_LIMITS: ResourceLimits = {
    maxCpuUsage: 70,        // 最大CPU使用率 (%)
    maxMemoryUsage: 80,     // 最大内存使用率 (%)
    batteryThreshold: 20,   // 电池电量阈值 (%)
    thermalThreshold: 40    // 温度阈值 (°C)
  };

  private isTestRunning = false;
  private shouldStopTest = false;

  // 高速网络检测和自适应配置
  private highSpeedNetworkDetected = false;
  private adaptiveConfig: ConcurrentTestConfig | null = null;

  /**
   * 执行自适应并发下载测试
   */
  async performConcurrentDownloadTest(
    baseUrl: string,
    networkType: NetworkType,
    progressCallback?: (progress: number, connectionId: number) => void,
    realTimeSpeedCallback?: (speedInfo: RealTimeSpeedInfo) => void
  ): Promise<ConcurrentTestResult> {

    console.log(`开始自适应并发测试，网络类型: ${networkType}`);

    // 检测高速网络并获取优化配置
    const config = await this.getOptimizedConfigForNetwork(baseUrl, networkType);
    console.log(`优化后并发配置: ${config.maxConnections}个连接, 每连接${(config.fileSize/1024/1024).toFixed(1)}MB`);

    // 检查资源限制
    const resourceCheck = await this.checkResourceLimits();
    if (!resourceCheck.canProceed) {
      console.warn('资源限制检查失败:', resourceCheck.reason);
      // 降级到单连接测试
      config.maxConnections = 1;
    }

    this.isTestRunning = true;
    this.shouldStopTest = false;

    try {
      // 预热连接
      await this.warmupConnections(baseUrl, config.maxConnections);

      // 执行并发测试
      const result = await this.executeConcurrentTest(baseUrl, config, progressCallback, realTimeSpeedCallback);
      
      console.log(`并发测试完成: 总速度=${result.totalSpeed.toFixed(2)}Mbps, 效率=${(result.efficiency*100).toFixed(1)}%`);
      
      return result;

    } catch (error) {
      console.error('并发测试失败:', error);
      throw new Error(`并发测试失败: ${error}`);
    } finally {
      this.isTestRunning = false;
    }
  }

  /**
   * 获取优化的网络配置（包含高速网络检测）
   */
  private async getOptimizedConfigForNetwork(baseUrl: string, networkType: NetworkType): Promise<ConcurrentTestConfig> {
    // 如果已有自适应配置，直接使用
    if (this.adaptiveConfig) {
      return this.adaptiveConfig;
    }

    // 获取基础配置
    let config = this.getConfigForNetworkType(networkType);

    // 对WiFi和以太网进行高速网络检测
    if (networkType === NetworkType.WIFI || networkType === NetworkType.ETHERNET) {
      const isHighSpeed = await this.detectHighSpeedNetwork(baseUrl);
      if (isHighSpeed) {
        config = this.getHighSpeedNetworkConfig(networkType);
        this.highSpeedNetworkDetected = true;
        console.log('🚀 检测到高速网络，启用千兆优化配置');
      }
    }

    // 缓存自适应配置
    this.adaptiveConfig = config;
    return config;
  }

  /**
   * 根据网络类型获取基础配置
   */
  private getConfigForNetworkType(networkType: NetworkType): ConcurrentTestConfig {
    let config: ConcurrentTestConfig;

    switch (networkType) {
      case NetworkType.WIFI:
        config = this.NETWORK_CONFIGS.WIFI;
        break;
      case NetworkType.ETHERNET:
        config = this.NETWORK_CONFIGS.ETHERNET;
        break;
      case NetworkType.CELLULAR_5G:
        config = this.NETWORK_CONFIGS.CELLULAR_5G;
        break;
      case NetworkType.CELLULAR_4G:
        config = this.NETWORK_CONFIGS.CELLULAR_4G;
        break;
      case NetworkType.CELLULAR_3G:
        config = this.NETWORK_CONFIGS.CELLULAR_3G;
        break;
      default:
        config = this.NETWORK_CONFIGS.UNKNOWN;
        break;
    }

    // 创建配置副本
    const result: ConcurrentTestConfig = {
      maxConnections: config.maxConnections,
      connectionTimeout: config.connectionTimeout,
      readTimeout: config.readTimeout,
      testDuration: config.testDuration,
      fileSize: config.fileSize
    };

    return result;
  }

  /**
   * 检测高速网络（千兆级别）
   */
  private async detectHighSpeedNetwork(baseUrl: string): Promise<boolean> {
    try {
      console.log('正在检测网络速度等级...');

      // 使用小文件快速测试来估算网络速度
      const testFileSize = 5 * 1024 * 1024; // 5MB
      const testUrl = `${baseUrl}?bytes=${testFileSize}`;

      const startTime = Date.now();
      const httpRequest = http.createHttp();

      let downloadedBytes = 0;
      httpRequest.on('dataReceive', (data) => {
        if (data instanceof ArrayBuffer) {
          downloadedBytes += data.byteLength;
        }
      });

      await httpRequest.request(testUrl, {
        method: http.RequestMethod.GET,
        expectDataType: http.HttpDataType.ARRAY_BUFFER,
        connectTimeout: 8000,
        readTimeout: 15000,
        header: {
          'Connection': 'keep-alive',
          'User-Agent': 'HarmonyOS-SpeedTest-Detection/1.0.0',
          'Cache-Control': 'no-cache'
        }
      });

      const duration = Date.now() - startTime;
      const speedMbps = (downloadedBytes * 8) / (duration * 1000); // Mbps

      httpRequest.destroy();

      console.log(`网络检测结果: ${speedMbps.toFixed(2)} Mbps`);

      // 如果速度超过100Mbps，认为是高速网络
      return speedMbps > 100;

    } catch (error) {
      console.warn('高速网络检测失败，使用默认配置:', error);
      return false;
    }
  }

  /**
   * 获取千兆网络优化配置
   */
  private getHighSpeedNetworkConfig(networkType: NetworkType): ConcurrentTestConfig {
    const baseConfig = this.getConfigForNetworkType(networkType);

    // 千兆网络专用优化配置
    const optimizedConfig: ConcurrentTestConfig = {
      maxConnections: Math.max(baseConfig.maxConnections * 2, 32), // 至少32个并发连接
      connectionTimeout: Math.max(baseConfig.connectionTimeout - 2000, 5000), // 减少连接超时
      readTimeout: baseConfig.readTimeout + 15000, // 增加读取超时以支持大文件
      testDuration: Math.max(baseConfig.testDuration + 15000, 45000), // 延长测试时间
      fileSize: Math.max(baseConfig.fileSize * 2, 100 * 1024 * 1024) // 至少100MB per connection
    };

    console.log(`千兆优化配置: ${optimizedConfig.maxConnections}个连接, ${(optimizedConfig.fileSize/1024/1024).toFixed(0)}MB/连接`);
    return optimizedConfig;
  }

  /**
   * 计算瞬时速度（借鉴LibreSpeed的精确计算方法）
   */
  private calculateInstantaneousSpeed(
    currentBytes: number,
    lastBytes: number,
    currentTime: number,
    lastTime: number
  ): number {
    const timeDelta = currentTime - lastTime;
    const bytesDelta = currentBytes - lastBytes;

    if (timeDelta <= 0 || bytesDelta <= 0) return 0;

    // 使用更精确的计算公式，避免浮点数精度问题
    const bitsPerSecond = (bytesDelta * 8) / (timeDelta / 1000);
    const mbps = bitsPerSecond / (1000 * 1000); // 转换为Mbps (1Mbps = 1,000,000 bps)

    return Math.max(0, mbps); // 确保不返回负值
  }

  /**
   * 计算平均速度（优化精度）
   */
  private calculateAverageSpeed(totalBytes: number, elapsedTime: number): number {
    if (elapsedTime <= 0 || totalBytes <= 0) return 0;

    const bitsPerSecond = (totalBytes * 8) / (elapsedTime / 1000);
    const mbps = bitsPerSecond / (1000 * 1000);

    return Math.max(0, mbps);
  }

  /**
   * 动态计算最优并发连接数（借鉴LibreSpeed的动态调整策略）
   */
  private calculateOptimalConnections(networkType: NetworkType, detectedSpeed: number): number {
    const baseConfig = this.getConfigForNetworkType(networkType);
    let optimalConnections = baseConfig.maxConnections;

    // 基于检测到的速度动态调整并发连接数
    if (detectedSpeed > 500) { // 千兆网络
      optimalConnections = Math.max(baseConfig.maxConnections * 3, 32);
      console.log(`🚀 千兆网络检测：调整并发连接数到 ${optimalConnections}`);
    } else if (detectedSpeed > 100) { // 高速网络
      optimalConnections = Math.max(baseConfig.maxConnections * 2, 16);
      console.log(`⚡ 高速网络检测：调整并发连接数到 ${optimalConnections}`);
    } else if (detectedSpeed > 50) { // 中速网络
      optimalConnections = Math.max(Math.floor(baseConfig.maxConnections * 1.5), 8);
      console.log(`📶 中速网络检测：调整并发连接数到 ${optimalConnections}`);
    } else {
      console.log(`📱 标准网络：保持默认并发连接数 ${optimalConnections}`);
    }

    return optimalConnections;
  }

  /**
   * 检查资源限制
   */
  private async checkResourceLimits(): Promise<ResourceCheckResult> {
    try {
      // 注意：HarmonyOS的系统API可能不同，这里是示例实现
      // 实际实现需要使用HarmonyOS提供的系统监控API
      
      // 检查电池电量（模拟）
      const batteryLevel = await this.getBatteryLevel();
      if (batteryLevel < this.RESOURCE_LIMITS.batteryThreshold) {
        const result: ResourceCheckResult = {
          canProceed: false,
          reason: `电池电量过低: ${batteryLevel}%`
        };
        return result;
      }

      // 检查内存使用率（模拟）
      const memoryUsage = await this.getMemoryUsage();
      if (memoryUsage > this.RESOURCE_LIMITS.maxMemoryUsage) {
        const result: ResourceCheckResult = {
          canProceed: false,
          reason: `内存使用率过高: ${memoryUsage}%`
        };
        return result;
      }

      // 检查设备温度（模拟）
      const temperature = await this.getDeviceTemperature();
      if (temperature > this.RESOURCE_LIMITS.thermalThreshold) {
        const result: ResourceCheckResult = {
          canProceed: false,
          reason: `设备温度过高: ${temperature}°C`
        };
        return result;
      }

      const successResult: ResourceCheckResult = { canProceed: true };
      return successResult;

    } catch (error) {
      console.warn('资源检查失败，继续执行测试:', error);
      const fallbackResult: ResourceCheckResult = { canProceed: true };
      return fallbackResult;
    }
  }

  /**
   * 预热连接
   */
  private async warmupConnections(baseUrl: string, connectionCount: number): Promise<void> {
    console.log(`预热 ${connectionCount} 个连接...`);

    const warmupPromises: Promise<void>[] = [];
    for (let i = 0; i < connectionCount; i++) {
      warmupPromises.push(this.warmupSingleConnection(baseUrl, i));
    }

    try {
      await Promise.allSettled(warmupPromises);
      console.log('连接预热完成');
    } catch (error) {
      console.warn('连接预热部分失败:', error);
    }
  }

  /**
   * 预热单个连接
   */
  private async warmupSingleConnection(baseUrl: string, connectionId: number): Promise<void> {
    try {
      const warmupUrl = baseUrl.replace(/bytes=\d+/, 'bytes=1024'); // 1KB预热
      
      const httpRequest = http.createHttp();
      await httpRequest.request(warmupUrl, {
        method: http.RequestMethod.GET,
        expectDataType: http.HttpDataType.ARRAY_BUFFER,
        connectTimeout: 5000,
        readTimeout: 3000,
        header: {
          'Connection': 'keep-alive',
          'User-Agent': `HarmonyOS-SpeedTest-Conn${connectionId}/1.0.0`
        }
      });
      
      httpRequest.destroy();
    } catch (error) {
      console.warn(`连接 ${connectionId} 预热失败:`, error);
    }
  }

  /**
   * 执行并发测试
   */
  private async executeConcurrentTest(
    baseUrl: string,
    config: ConcurrentTestConfig,
    progressCallback?: (progress: number, connectionId: number) => void,
    realTimeSpeedCallback?: (speedInfo: RealTimeSpeedInfo) => void
  ): Promise<ConcurrentTestResult> {
    
    const testUrl = baseUrl.replace(/bytes=\d+/, `bytes=${config.fileSize}`);
    const connectionPromises: Promise<ConnectionResult>[] = [];

    // 启动所有并发连接
    for (let i = 0; i < config.maxConnections; i++) {
      const promise = this.executeConnectionTest(
        testUrl,
        i,
        config,
        progressCallback,
        realTimeSpeedCallback
      );
      connectionPromises.push(promise);
    }

    // 等待所有连接完成
    const results = await Promise.allSettled(connectionPromises);
    const connectionResults: ConnectionResult[] = [];

    // 处理结果
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        connectionResults.push(result.value);
      } else {
        console.warn(`连接 ${index} 失败:`, result.reason);
        connectionResults.push({
          connectionId: index,
          success: false,
          bytesDownloaded: 0,
          duration: 0,
          averageSpeed: 0,
          error: result.reason?.toString()
        });
      }
    });

    // 计算总体结果
    return this.aggregateResults(connectionResults);
  }

  /**
   * 执行单个连接测试
   */
  private async executeConnectionTest(
    testUrl: string,
    connectionId: number,
    config: ConcurrentTestConfig,
    progressCallback?: (progress: number, connectionId: number) => void,
    realTimeSpeedCallback?: (speedInfo: RealTimeSpeedInfo) => void
  ): Promise<ConnectionResult> {
    
    console.log(`连接 ${connectionId} 开始测试...`);
    
    const startTime = Date.now();
    let bytesDownloaded = 0;
    let lastSpeedUpdateTime = startTime;
    let lastBytesDownloaded = 0;

    try {
      const httpRequest = http.createHttp();

      // 设置进度和实时速度回调
      if (progressCallback || realTimeSpeedCallback) {
        httpRequest.on('dataReceive', (data) => {
          if (data instanceof ArrayBuffer) {
            bytesDownloaded += data.byteLength;
            const currentTime = Date.now();
            const elapsedTime = currentTime - startTime;

            // 计算进度
            const progress = Math.min(100, (bytesDownloaded / config.fileSize) * 100);
            if (progressCallback) {
              progressCallback(progress, connectionId);
            }

            // 计算实时速度（每200ms更新一次，借鉴LibreSpeed的高频更新策略）
            if (realTimeSpeedCallback && currentTime - lastSpeedUpdateTime >= 200) {
              const timeDelta = currentTime - lastSpeedUpdateTime;
              const bytesDelta = bytesDownloaded - lastBytesDownloaded;

              // 瞬时速度 (Mbps) - 使用更精确的计算方法
              const currentSpeed = this.calculateInstantaneousSpeed(
                bytesDownloaded, lastBytesDownloaded, currentTime, lastSpeedUpdateTime
              );

              // 平均速度 (Mbps) - 使用更精确的计算方法
              const averageSpeed = this.calculateAverageSpeed(bytesDownloaded, elapsedTime);

              const speedInfo: RealTimeSpeedInfo = {
                currentSpeed: currentSpeed,
                averageSpeed: averageSpeed,
                totalBytes: bytesDownloaded,
                elapsedTime: elapsedTime,
                connectionId: connectionId,
                progress: progress
              };

              realTimeSpeedCallback(speedInfo);

              // 更新上次记录
              lastSpeedUpdateTime = currentTime;
              lastBytesDownloaded = bytesDownloaded;
            }
          }
        });
      }

      const response = await httpRequest.request(testUrl, {
        method: http.RequestMethod.GET,
        expectDataType: http.HttpDataType.ARRAY_BUFFER,
        connectTimeout: config.connectionTimeout,
        readTimeout: config.readTimeout,
        header: {
          'Connection': 'keep-alive',
          'User-Agent': `HarmonyOS-SpeedTest-Conn${connectionId}/1.0.0`,
          'Cache-Control': 'no-cache'
        }
      });

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      if (response.result && response.result instanceof ArrayBuffer) {
        bytesDownloaded = response.result.byteLength;
      }

      httpRequest.destroy();

      const averageSpeed = (bytesDownloaded * 8) / (duration * 1000000); // Mbps

      console.log(`连接 ${connectionId} 完成: ${(bytesDownloaded/1024/1024).toFixed(2)}MB, ${duration.toFixed(2)}s, ${averageSpeed.toFixed(2)}Mbps`);

      return {
        connectionId,
        success: true,
        bytesDownloaded,
        duration,
        averageSpeed,
      };

    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      console.warn(`连接 ${connectionId} 失败:`, (error as Error).message);

      return {
        connectionId,
        success: false,
        bytesDownloaded,
        duration,
        averageSpeed: 0,
        error: (error as Error).message
      };
    }
  }

  /**
   * 聚合并发测试结果
   */
  private aggregateResults(connectionResults: ConnectionResult[]): ConcurrentTestResult {
    const successfulResults = connectionResults.filter(r => r.success);
    const totalBytesDownloaded = connectionResults.reduce((sum, r) => sum + r.bytesDownloaded, 0);
    
    // 计算总体速度 - 使用重叠时间段的方法
    const maxDuration = Math.max(...connectionResults.map(r => r.duration));
    const totalSpeed = maxDuration > 0 ? (totalBytesDownloaded * 8) / (maxDuration * 1000000) : 0;

    // 计算并发效率
    const theoreticalSpeed = successfulResults.reduce((sum, r) => sum + r.averageSpeed, 0);
    const efficiency = theoreticalSpeed > 0 ? Math.min(1, totalSpeed / theoreticalSpeed) : 0;

    const result: ConcurrentTestResult = {
      totalSpeed,
      connectionResults,
      successfulConnections: successfulResults.length,
      totalBytesDownloaded,
      totalDuration: maxDuration,
      efficiency
    };

    // 记录详细日志
    console.log('并发测试聚合结果:');
    console.log(`- 成功连接: ${result.successfulConnections}/${connectionResults.length}`);
    console.log(`- 总下载量: ${(result.totalBytesDownloaded/1024/1024).toFixed(2)}MB`);
    console.log(`- 总耗时: ${result.totalDuration.toFixed(2)}s`);
    console.log(`- 总速度: ${result.totalSpeed.toFixed(2)}Mbps`);
    console.log(`- 并发效率: ${(result.efficiency*100).toFixed(1)}%`);

    return result;
  }

  /**
   * 停止并发测试
   */
  stopTest(): void {
    this.shouldStopTest = true;
    console.log('并发测试已停止');
  }

  /**
   * 获取电池电量（模拟实现）
   */
  private async getBatteryLevel(): Promise<number> {
    // 实际实现应该使用HarmonyOS的电池API
    // 这里返回模拟值
    return 80;
  }

  /**
   * 获取内存使用率（模拟实现）
   */
  private async getMemoryUsage(): Promise<number> {
    // 实际实现应该使用HarmonyOS的内存监控API
    // 这里返回模拟值
    return 60;
  }

  /**
   * 获取设备温度（模拟实现）
   */
  private async getDeviceTemperature(): Promise<number> {
    // 实际实现应该使用HarmonyOS的温度监控API
    // 这里返回模拟值
    return 35;
  }

  /**
   * 检查是否应该停止测试
   */
  private checkShouldStop(): boolean {
    return this.shouldStopTest;
  }
}
