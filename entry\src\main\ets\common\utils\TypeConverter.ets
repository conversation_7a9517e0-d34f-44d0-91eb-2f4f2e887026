// 类型转换工具类
// 遵循ArkTS规范，提供安全的类型转换方法

import { SpeedTestResult, NetworkInfo, TestServer } from '../types/SpeedTestModels';

/**
 * 类型转换工具类
 */
export class TypeConverter {
  /**
   * 转换为数字类型
   */
  static toNumber(value: string | number | undefined): number {
    if (typeof value === 'number') {
      return value;
    }
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  /**
   * 转换为字符串类型
   */
  static toString(value: string | number | undefined): string {
    if (typeof value === 'string') {
      return value;
    }
    if (typeof value === 'number') {
      return value.toString();
    }
    return '';
  }

  /**
   * 转换为布尔类型
   */
  static toBoolean(value: string | number | boolean | undefined): boolean {
    if (typeof value === 'boolean') {
      return value;
    }
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true' || value === '1';
    }
    if (typeof value === 'number') {
      return value !== 0;
    }
    return false;
  }

  /**
   * 安全转换记录对象
   */
  static convertRecord<T>(record: Record<string, string | number>): T {
    return record as T;
  }

  /**
   * 批量转换记录数组
   */
  static convertRecords<T>(records: Record<string, string | number>[]): T[] {
    return records.map(record => TypeConverter.convertRecord<T>(record));
  }

  /**
   * 转换为SpeedTestResult
   */
  static convertToSpeedTestResult(data: Record<string, string | number | boolean | object>): SpeedTestResult {
    const result: SpeedTestResult = {
      id: TypeConverter.toString(data.id as string || ''),
      timestamp: TypeConverter.toNumber(data.timestamp as string | number || 0),
      downloadSpeed: TypeConverter.toNumber(data.downloadSpeed as string | number || 0),
      uploadSpeed: TypeConverter.toNumber(data.uploadSpeed as string | number || 0),
      latency: TypeConverter.toNumber(data.latency as string | number || 0),
      jitter: TypeConverter.toNumber(data.jitter as string | number || 0),
      packetLoss: TypeConverter.toNumber(data.packetLoss as string | number || 0),
      networkInfo: TypeConverter.convertToNetworkInfo((data.networkInfo || {}) as Record<string, string | number | boolean>),
        testServer: TypeConverter.convertToTestServer((data.testServer || {}) as Record<string, string | number | boolean>),
      location: TypeConverter.toString(data.location as string || ''),
      duration: TypeConverter.toNumber(data.duration as string | number || 0)
    };
    return result;
  }

  /**
   * 转换为NetworkInfo
   */
  static toNetworkInfo(data: Record<string, string | number | boolean>): NetworkInfo {
    return {
      type: TypeConverter.toString(data.type as string || 'Unknown'),
      operator: TypeConverter.toString(data.operator as string || 'Unknown'),
      ipAddress: TypeConverter.toString(data.ipAddress as string || '0.0.0.0'),
      signalStrength: TypeConverter.toNumber(data.signalStrength as string | number || 0),
      isConnected: TypeConverter.toBoolean(data.isConnected as string | boolean || false)
    };
  }

  /**
   * 转换网络信息
   */
  static convertToNetworkInfo(data: Record<string, string | number | boolean>): NetworkInfo {
    return {
      type: TypeConverter.toString(data.type as string || 'Unknown'),
      operator: TypeConverter.toString(data.operator as string || 'Unknown'),
      ipAddress: TypeConverter.toString(data.ipAddress as string || '0.0.0.0'),
      signalStrength: TypeConverter.toNumber(data.signalStrength as string | number || 0),
      isConnected: TypeConverter.toBoolean(data.isConnected as string | boolean || false)
    };
  }

  /**
   * 转换为TestServer
   */
  static toTestServer(data: Record<string, string | number | boolean>): TestServer {
    return {
      id: TypeConverter.toString(data.id as string || 'default'),
      name: TypeConverter.toString(data.name as string || 'Default Server'),
      url: TypeConverter.toString(data.url as string || ''),
      location: TypeConverter.toString(data.location as string || 'Unknown'),
      latency: TypeConverter.toNumber(data.latency as string | number || 0)
    };
  }

  /**
   * 转换测试服务器信息
   */
  static convertToTestServer(data: Record<string, string | number | boolean>): TestServer {
    return {
      id: TypeConverter.toString(data.id as string || 'default'),
      name: TypeConverter.toString(data.name as string || 'Default Server'),
      url: TypeConverter.toString(data.url as string || ''),
      location: TypeConverter.toString(data.location as string || 'Unknown'),
      latency: TypeConverter.toNumber(data.latency as string | number || 0)
    };
  }

  /**
   * 格式化速度值
   */
  static formatSpeed(speed: number): string {
    if (speed >= 1000) {
      const gbps = speed / 1000;
      return `${gbps.toFixed(2)} Gbps`;
    }
    return `${speed.toFixed(2)} Mbps`;
  }

  /**
   * 格式化延迟值
   */
  static formatLatency(latency: number): string {
    return `${latency.toFixed(0)} ms`;
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 生成唯一ID
   */
  static generateUniqueId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
  
  /**
   * 生成唯一ID（别名方法）
   */
  static generateId(): string {
    return TypeConverter.generateUniqueId();
  }

  /**
   * 验证IP地址格式
   */
  static isValidIP(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  }

  /**
   * 验证URL格式
   */
  static isValidURL(url: string): boolean {
    try {
      const urlPattern = /^(https?:\/\/)[\da-z\.-]+\.[a-z\.]{2,6}([\/\w \.-]*)*\/?$/;
      return urlPattern.test(url);
    } catch {
      return false;
    }
  }
}