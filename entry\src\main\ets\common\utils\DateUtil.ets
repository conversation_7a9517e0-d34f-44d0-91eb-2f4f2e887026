// 日期工具类
// 遵循ArkTS规范，避免使用this引用在静态方法中

/**
 * 日期工具类
 */
export class DateUtil {
  /**
   * 格式化日期
   */
  static formatDate(timestamp: number, format: string = 'YYYY-MM-DD'): string {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    let result = format;
    result = result.replace('YYYY', year.toString());
    result = result.replace('MM', month);
    result = result.replace('DD', day);
    result = result.replace('HH', hours);
    result = result.replace('mm', minutes);
    result = result.replace('ss', seconds);

    return result;
  }

  /**
   * 格式化时间
   */
  static formatTime(timestamp: number): string {
    return DateUtil.formatDate(timestamp, 'HH:mm');
  }

  /**
   * 格式化日期时间
   */
  static formatDateTime(timestamp: number | Date): string {
    const date = typeof timestamp === 'number' ? new Date(timestamp) : timestamp;
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 获取相对时间描述
   */
  static getRelativeTime(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天前`;
    }
    if (hours > 0) {
      return `${hours}小时前`;
    }
    if (minutes > 0) {
      return `${minutes}分钟前`;
    }
    if (seconds > 0) {
      return `${seconds}秒前`;
    }
    return '刚刚';
  }

  /**
   * 获取今天的开始时间戳
   */
  static getTodayStart(): number {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today.getTime();
  }

  /**
   * 获取今天的结束时间戳
   */
  static getTodayEnd(): number {
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    return today.getTime();
  }

  /**
   * 获取本周开始时间戳
   */
  static getThisWeekStart(): number {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - dayOfWeek);
    startOfWeek.setHours(0, 0, 0, 0);
    return startOfWeek.getTime();
  }
  
  /**
   * 获取本周结束时间戳
   */
  static getThisWeekEnd(): number {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const endOfWeek = new Date(now);
    endOfWeek.setDate(now.getDate() + (6 - dayOfWeek));
    endOfWeek.setHours(23, 59, 59, 999);
    return endOfWeek.getTime();
  }
  
  /**
   * 获取本月开始时间戳
   */
  static getThisMonthStart(): number {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    startOfMonth.setHours(0, 0, 0, 0);
    return startOfMonth.getTime();
  }
  
  /**
   * 获取本月结束时间戳
   */
  static getThisMonthEnd(): number {
    const now = new Date();
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    endOfMonth.setHours(23, 59, 59, 999);
    return endOfMonth.getTime();
  }

  /**
   * 获取本月的开始时间戳
   */
  static getMonthStart(): number {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    startOfMonth.setHours(0, 0, 0, 0);
    return startOfMonth.getTime();
  }

  /**
   * 获取本年的开始时间戳
   */
  static getYearStart(): number {
    const today = new Date();
    const startOfYear = new Date(today.getFullYear(), 0, 1);
    startOfYear.setHours(0, 0, 0, 0);
    return startOfYear.getTime();
  }

  /**
   * 判断是否为今天
   */
  static isToday(timestamp: number): boolean {
    const today = DateUtil.getTodayStart();
    const tomorrow = today + 24 * 60 * 60 * 1000;
    return timestamp >= today && timestamp < tomorrow;
  }

  /**
   * 判断是否为本周
   */
  static isThisWeek(timestamp: number): boolean {
    const weekStart = DateUtil.getThisWeekStart();
    const weekEnd = weekStart + 7 * 24 * 60 * 60 * 1000;
    return timestamp >= weekStart && timestamp < weekEnd;
  }

  /**
   * 判断是否为本月
   */
  static isThisMonth(timestamp: number): boolean {
    const date = new Date(timestamp);
    const today = new Date();
    return date.getFullYear() === today.getFullYear() && 
           date.getMonth() === today.getMonth();
  }

  /**
   * 获取时间差描述
   */
  static getTimeDifference(startTime: number, endTime: number): string {
    const diff = endTime - startTime;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    }
    if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    }
    return `${seconds}秒`;
  }
}