import { SpeedTestService } from '../services/SpeedTestService';
import { NetworkService } from '../services/NetworkService';
import { StorageService } from '../services/StorageService';
import { SpeedTestResult, NetworkInfo, TestServer, TestStatus, TestProgress } from '../common/types/SpeedTestModels';
import { TypeConverter } from '../common/utils/TypeConverter';
import { SimpleSpeedometer, NetworkQualityScore } from '../common/components/Speedometer';
import { RealTimeSpeedDisplay } from '../common/components/RealTimeSpeedDisplay';
import router from '@ohos.router';
import { promptAction } from '@kit.ArkUI';
import { common } from '@kit.AbilityKit';

@Entry
@Component
struct Index {
  @State downloadSpeed: number = 0;
  @State uploadSpeed: number = 0;
  @State latency: number = 0;
  @State jitter: number = 0;
  @State testStatus: string = TestStatus.IDLE;
  @State testProgress: number = 0;
  @State networkInfo: NetworkInfo | null = null;
  @State testServer: TestServer | null = null;
  @State isTestRunning: boolean = false;
  @State statusMessage: string = '点击开始测速';
  @State networkQualityScore: number = 0;
  @State networkQualityDescription: string = '';

  // 实时速度显示相关状态
  @State currentDownloadSpeed: number = 0;
  @State currentUploadSpeed: number = 0;
  @State averageDownloadSpeed: number = 0;
  @State averageUploadSpeed: number = 0;
  @State testPhase: string = 'idle';
  @State elapsedTime: number = 0;
  @State totalBytes: number = 0;
  
  private speedTestService: SpeedTestService = new SpeedTestService();
  private networkService = new NetworkService();
  private storageService = new StorageService();
  
  async aboutToAppear() {
    await this.initializeServices();
    await this.loadNetworkInfo();
  }
  
  async initializeServices() {
    try {
      // 获取应用上下文并初始化存储服务
      const uiContext = this.getUIContext();
      const context = uiContext.getHostContext() as common.UIAbilityContext;
      await this.storageService.initialize(context);
      console.log('服务初始化成功');
    } catch (error) {
      console.error('服务初始化失败:', error);
    }
  }
  
  async loadNetworkInfo() {
    try {
      this.networkInfo = await this.networkService.getNetworkInfo();
      this.testServer = await this.networkService.getBestServer();
    } catch (error) {
      console.error('加载网络信息失败:', error);
    }
  }
  
  stopSpeedTest() {
    if (this.isTestRunning) {
      this.speedTestService.stopTest();
      this.isTestRunning = false;
      this.testProgress = 0;
      this.statusMessage = '测试已停止';
      this.testStatus = TestStatus.IDLE;
    }
  }

  async startSpeedTest() {
    if (this.isTestRunning) {
      return;
    }

    this.isTestRunning = true;
    this.testProgress = 0;
    this.downloadSpeed = 0;
    this.uploadSpeed = 0;
    this.latency = 0;
    this.jitter = 0;
    this.networkQualityScore = 0;
    this.networkQualityDescription = '';

    // 重置实时速度显示状态
    this.currentDownloadSpeed = 0;
    this.currentUploadSpeed = 0;
    this.averageDownloadSpeed = 0;
    this.averageUploadSpeed = 0;
    this.testPhase = 'idle';
    this.elapsedTime = 0;
    this.totalBytes = 0;

    try {
      // 检查网络连接
      const isConnected = await this.networkService.checkConnectivity();
      if (!isConnected) {
        this.statusMessage = '网络连接失败，请检查网络设置';
        this.isTestRunning = false;
        return;
      }

      // 获取最新网络信息
      await this.loadNetworkInfo();

      // 开始完整测速（带实时速度回调）
      const testStartTime = Date.now();
      const result = await this.speedTestService.performFullSpeedTest(
        (progress: TestProgress) => {
          this.testStatus = progress.phase;
          this.testProgress = progress.progress;
          this.testPhase = progress.phase;
          this.elapsedTime = Date.now() - testStartTime;
          this.updateStatusMessage(progress.message);
        },
        (downloadSpeed: number) => {
          this.downloadSpeed = downloadSpeed;
          this.averageDownloadSpeed = downloadSpeed;
        },
        (uploadSpeed: number) => {
          this.uploadSpeed = uploadSpeed;
          this.averageUploadSpeed = uploadSpeed;
        },
        (latency: number) => {
          this.latency = latency;
        },
        // 实时速度回调
        (speedInfo) => {
          this.currentDownloadSpeed = speedInfo.currentSpeed;
          this.averageDownloadSpeed = speedInfo.averageSpeed;
          this.totalBytes = speedInfo.totalBytes;
          this.elapsedTime = speedInfo.elapsedTime;
          this.testProgress = speedInfo.progress;
        }
      );
      
      // 更新测试结果
      this.downloadSpeed = result.downloadSpeed;
      this.uploadSpeed = result.uploadSpeed;
      this.latency = result.latency;
      this.jitter = result.jitter;
      
      // 计算网络质量评分
      this.networkQualityScore = this.networkService.getNetworkQualityScore(
        result.downloadSpeed, result.uploadSpeed, result.latency
      );
      this.networkQualityDescription = this.networkService.getNetworkQualityDescription(
        this.networkQualityScore
      );
      
      // 保存测试结果
      await this.storageService.saveTestResult(result);
      
      this.statusMessage = '测速完成';
      this.testStatus = TestStatus.COMPLETED;
      
    } catch (error) {
      console.error('测速失败:', error);
      this.statusMessage = '测速失败，请重试';
      this.testStatus = TestStatus.ERROR;
    } finally {
      this.isTestRunning = false;
      this.testProgress = 100;
    }
  }
  
  updateStatusMessage(status: string) {
    switch (status) {
      case TestStatus.TESTING_LATENCY:
        this.statusMessage = '正在测试延迟...';
        break;
      case TestStatus.TESTING_DOWNLOAD:
        this.statusMessage = '正在测试下载速度...';
        break;
      case TestStatus.TESTING_UPLOAD:
        this.statusMessage = '正在测试上传速度...';
        break;
      case TestStatus.TESTING_JITTER:
        this.statusMessage = '正在测试抖动...';
        break;
      default:
        this.statusMessage = '正在测速...';
        break;
    }
  }
  
  navigateToHistory() {
    router.pushUrl({
      url: 'pages/History'
    }).catch((error: Error) => {
      console.error('导航到历史页面失败:', error);
    });
  }

  navigateToSettings() {
    router.pushUrl({
      url: 'pages/Settings'
    }).catch((error: Error) => {
      console.error('导航到设置页面失败:', error);
    });
  }
  
  build() {
    Column() {
      // 标题栏
      Row() {
        Text('网速测试')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
        
        Blank()
        
        Row() {
          Button('历史记录')
            .fontSize(14)
            .backgroundColor('#007AFF')
            .borderRadius(8)
            .padding({ left: 12, right: 12, top: 6, bottom: 6 })
            .onClick(() => {
              this.navigateToHistory();
            })

          Button('设置')
            .fontSize(14)
            .backgroundColor('#34C759')
            .borderRadius(8)
            .padding({ left: 12, right: 12, top: 6, bottom: 6 })
            .margin({ left: 8 })
            .onClick(() => {
              this.navigateToSettings();
            })
        }
      }
      .width('100%')
      .padding({ left: 20, right: 20, top: 20 })
      
      // 网络信息
      if (this.networkInfo) {
        Row() {
          Text(`网络类型: ${this.networkService.getNetworkTypeDescription(this.networkInfo.type)}`)
            .fontSize(14)
            .fontColor('#666666')
          
          Blank()
          
          Text(`IP: ${this.networkInfo.ipAddress}`)
            .fontSize(14)
            .fontColor('#666666')
        }
        .width('100%')
        .padding({ left: 20, right: 20, top: 10 })
      }
      
      // 速度显示区域
      Column() {
        if (this.isTestRunning) {
          // 实时速度显示组件（测试运行时）
          RealTimeSpeedDisplay({
            currentDownloadSpeed: this.currentDownloadSpeed,
            currentUploadSpeed: this.currentUploadSpeed,
            averageDownloadSpeed: this.averageDownloadSpeed,
            averageUploadSpeed: this.averageUploadSpeed,
            latency: this.latency,
            jitter: this.jitter,
            testPhase: this.testPhase,
            isRunning: this.isTestRunning,
            progress: this.testProgress,
            elapsedTime: this.elapsedTime,
            totalBytes: this.totalBytes
          })
        } else {
          // 静态测速仪表盘（测试未运行时）
          SimpleSpeedometer({
            downloadSpeed: this.downloadSpeed,
            uploadSpeed: this.uploadSpeed,
            latency: this.latency,
            isRunning: this.isTestRunning
          })
          .margin({ top: 20, bottom: 20 })

          // 网络质量评分
          if (this.networkQualityScore > 0) {
            NetworkQualityScore({
              score: this.networkQualityScore,
              description: this.networkQualityDescription
            })
            .margin({ top: 20 })
          }
        }
        
        // 状态信息
        Text(this.statusMessage)
          .fontSize(16)
          .fontColor('#666666')
          .textAlign(TextAlign.Center)
          .margin({ top: 20 })
        
        // 测试控制按钮
        Row() {
          if (this.isTestRunning) {
            Button('停止测试')
              .width(180)
              .height(50)
              .fontSize(18)
              .fontWeight(FontWeight.Medium)
              .backgroundColor('#FF3B30')
              .fontColor('#FFFFFF')
              .borderRadius(25)
              .onClick(() => {
                this.stopSpeedTest();
              })
          } else {
            Button('开始测速')
              .width(200)
              .height(50)
              .fontSize(18)
              .fontWeight(FontWeight.Medium)
              .backgroundColor('#007AFF')
              .fontColor('#FFFFFF')
              .borderRadius(25)
              .onClick(() => {
                this.startSpeedTest();
              })
          }
        }
        .margin({ top: 40 })
        .justifyContent(FlexAlign.Center)
      }
      .layoutWeight(1)
      .width('100%')
      .padding({ left: 20, right: 20 })
      .justifyContent(FlexAlign.Center)
      
      // 底部信息
      if (this.testServer) {
        Text(`测试服务器: ${this.testServer.name} (${this.testServer.location})`)
          .fontSize(12)
          .fontColor('#999999')
          .textAlign(TextAlign.Center)
          .margin({ bottom: 20 })
      }
    }
    .height('100%')
    .width('100%')
    .backgroundColor('#F2F2F7')
  }
  
  getQualityScoreColor(score: number): string {
    if (score >= 90) {
      return '#34C759'; // 绿色
    } else if (score >= 75) {
      return '#007AFF'; // 蓝色
    } else if (score >= 60) {
      return '#FF9500'; // 橙色
    } else {
      return '#FF3B30'; // 红色
    }
  }
}