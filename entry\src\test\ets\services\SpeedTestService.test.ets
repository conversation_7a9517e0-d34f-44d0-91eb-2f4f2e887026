// SpeedTestService 单元测试
// 验证网络测试功能的准确性和稳定性

import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';
import { SpeedTestService } from '../../../main/ets/services/SpeedTestService';
import { TestProgress, TestPhase } from '../../../main/ets/common/types/SpeedTestModels';

export default function speedTestServiceTest() {
  describe('SpeedTestService', function () {
    let speedTestService: SpeedTestService;

    beforeEach(function () {
      speedTestService = new SpeedTestService();
    });

    afterEach(function () {
      speedTestService.stopTest();
    });

    /**
     * 延迟测试单元测试
     */
    describe('延迟测试', function () {
      it('应该返回合理的延迟值', async function () {
        const latency = await speedTestService.testLatency();
        
        expect(latency).assertGreaterThan(0);
        expect(latency).assertLessThan(10000); // 10秒内
        expect(typeof latency).assertEqual('number');
      });

      it('应该处理网络错误', async function () {
        try {
          await speedTestService.testLatency('https://invalid-url-for-test.com');
          expect(false).assertTrue(); // 不应该到达这里
        } catch (error) {
          expect(error).assertInstanceOf(Error);
        }
      });

      it('应该支持缓存功能', async function () {
        const latency1 = await speedTestService.testLatency(undefined, true);
        const latency2 = await speedTestService.testLatency(undefined, true);
        
        // 第二次调用应该使用缓存，结果相同
        expect(latency1).assertEqual(latency2);
      });

      it('应该验证延迟值的合理性', function () {
        // 测试私有方法的逻辑（通过反射或公开测试方法）
        const validLatencies = [50, 100, 200, 500];
        const invalidLatencies = [-1, 0, 15000];
        
        validLatencies.forEach(latency => {
          expect(speedTestService['isValidLatency'](latency)).assertTrue();
        });
        
        invalidLatencies.forEach(latency => {
          expect(speedTestService['isValidLatency'](latency)).assertFalse();
        });
      });
    });

    /**
     * 下载速度测试单元测试
     */
    describe('下载速度测试', function () {
      it('应该返回合理的下载速度', async function () {
        const speed = await speedTestService.testDownloadSpeed();
        
        expect(speed).assertGreaterThan(0);
        expect(speed).assertLessThan(2000); // 2000 Mbps内
        expect(typeof speed).assertEqual('number');
      });

      it('应该处理超时错误', async function () {
        // 使用一个会超时的大文件URL进行测试
        try {
          const speed = await speedTestService.testDownloadSpeed();
          expect(speed).assertGreaterThan(0);
        } catch (error) {
          // 超时错误是可接受的
          expect(error.message).assertContain('timeout');
        }
      });

      it('应该验证下载速度的合理性', function () {
        const validSpeeds = [0.5, 10, 50, 100, 500];
        const invalidSpeeds = [-1, 0, 0.05, 3000];
        
        validSpeeds.forEach(speed => {
          expect(speedTestService['isValidDownloadSpeed'](speed)).assertTrue();
        });
        
        invalidSpeeds.forEach(speed => {
          expect(speedTestService['isValidDownloadSpeed'](speed)).assertFalse();
        });
      });

      it('应该正确计算速度单位', function () {
        // 测试速度计算公式
        const bytes = 5000000; // 5MB
        const duration = 10; // 10秒
        const expectedSpeedMbps = (bytes * 8) / (duration * 1000000); // 4 Mbps
        
        expect(expectedSpeedMbps).assertEqual(4);
      });
    });

    /**
     * 抖动测试单元测试
     */
    describe('抖动测试', function () {
      it('应该返回合理的抖动值', async function () {
        const jitter = await speedTestService.testJitter();
        
        expect(jitter).assertGreaterOrEqual(0);
        expect(jitter).assertLessThan(5000); // 5秒内
        expect(typeof jitter).assertEqual('number');
      });

      it('应该正确计算抖动值', function () {
        const latencies = [100, 120, 80, 110, 90, 130, 95, 105];
        const jitter = speedTestService['calculateJitter'](latencies);
        
        expect(jitter).assertGreaterThan(0);
        expect(typeof jitter).assertEqual('number');
      });

      it('应该处理样本不足的情况', async function () {
        try {
          // 模拟网络完全不可用的情况
          await speedTestService.testJitter('https://invalid-url-for-test.com', 3);
        } catch (error) {
          expect(error.message).assertContain('样本不足');
        }
      });
    });

    /**
     * 数据处理算法测试
     */
    describe('数据处理算法', function () {
      it('应该正确移除异常值', function () {
        const values = [10, 12, 11, 13, 9, 100, 8, 14, 10, 12]; // 100是异常值
        const filtered = speedTestService['removeOutliers'](values);
        
        expect(filtered.length).assertLessThan(values.length);
        expect(filtered.includes(100)).assertFalse();
      });

      it('应该正确计算最优延迟值', function () {
        const measurements = [50, 52, 48, 51, 49, 53, 47, 50];
        const optimal = speedTestService['calculateOptimalLatency'](measurements);
        
        expect(optimal).assertGreaterThan(40);
        expect(optimal).assertLessThan(60);
      });

      it('应该处理空数据', function () {
        try {
          speedTestService['calculateOptimalLatency']([]);
        } catch (error) {
          expect(error.message).assertContain('没有有效的延迟测量数据');
        }
      });
    });

    /**
     * 网络环境检测测试
     */
    describe('网络环境检测', function () {
      it('应该检测网络环境并返回合理配置', async function () {
        const networkEnv = await speedTestService['detectNetworkEnvironment']();
        
        expect(networkEnv.estimatedBandwidth).assertGreaterThan(0);
        expect(networkEnv.recommendedFileSize).assertGreaterThan(0);
        expect(['excellent', 'good', 'fair', 'poor']).assertContain(networkEnv.networkQuality);
      });

      it('应该在网络检测失败时返回默认配置', async function () {
        // 模拟网络检测失败
        const originalQuickLatencyTest = speedTestService['quickLatencyTest'];
        speedTestService['quickLatencyTest'] = async () => {
          throw new Error('网络不可用');
        };

        const networkEnv = await speedTestService['detectNetworkEnvironment']();
        
        expect(networkEnv.networkQuality).assertEqual('fair');
        expect(networkEnv.estimatedBandwidth).assertEqual(10);
        
        // 恢复原方法
        speedTestService['quickLatencyTest'] = originalQuickLatencyTest;
      });
    });

    /**
     * 错误处理测试
     */
    describe('错误处理', function () {
      it('应该正确处理网络超时', async function () {
        try {
          // 使用一个会超时的请求
          await speedTestService['executeRequest']('https://httpstat.us/200?sleep=70000', {
            method: 'GET',
            connectTimeout: 1000,
            readTimeout: 1000
          });
        } catch (error) {
          expect(error.message).assertContain('timeout');
        }
      });

      it('应该正确判断是否需要重试', function () {
        const retryableErrors = ['timeout', 'network error', 'connection failed'];
        const nonRetryableErrors = ['SSL error', 'certificate error', 'exceeds the maximum limit'];
        
        retryableErrors.forEach(errorMsg => {
          expect(speedTestService['shouldRetry'](errorMsg, 0, 2)).assertTrue();
        });
        
        nonRetryableErrors.forEach(errorMsg => {
          expect(speedTestService['shouldRetry'](errorMsg, 0, 2)).assertFalse();
        });
      });

      it('应该计算正确的重试延迟', function () {
        const delay1 = speedTestService['calculateRetryDelay'](0, 'network error');
        const delay2 = speedTestService['calculateRetryDelay'](1, 'timeout');
        
        expect(delay1).assertEqual(1000); // 第一次重试1秒
        expect(delay2).assertEqual(4000); // 超时错误第二次重试4秒
      });
    });

    /**
     * 综合测试
     */
    describe('综合测试', function () {
      it('应该能够完成完整的网络测试', async function () {
        let progressUpdates = 0;
        
        const result = await speedTestService.runFullTest((progress: TestProgress) => {
          progressUpdates++;
          expect(progress.phase).assertInstanceOf(TestPhase);
          expect(progress.progress).assertGreaterOrEqual(0);
          expect(progress.progress).assertLessOrEqual(100);
        });
        
        expect(result.downloadSpeed).assertGreaterThan(0);
        expect(result.uploadSpeed).assertGreaterThan(0);
        expect(result.latency).assertGreaterThan(0);
        expect(result.jitter).assertGreaterOrEqual(0);
        expect(progressUpdates).assertGreaterThan(0);
      });

      it('应该能够停止正在进行的测试', async function () {
        const testPromise = speedTestService.runFullTest();
        
        // 立即停止测试
        speedTestService.stopTest();
        
        try {
          await testPromise;
        } catch (error) {
          // 测试被停止是预期的
          expect(error.message).assertContain('测试被停止');
        }
      });
    });
  });
}
