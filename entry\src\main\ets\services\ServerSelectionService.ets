// 智能服务器选择服务
// 基于地理位置和网络性能的最佳测试服务器选择

import http from '@ohos.net.http';
import { TestServer } from '../common/types/SpeedTestModels';

/**
 * 服务器性能指标接口
 */
interface ServerMetrics {
  serverId: string;
  latency: number;
  stability: number;  // 延迟稳定性评分 (0-100)
  loadScore: number;  // 负载评分 (0-100, 越低越好)
  successRate: number; // 成功率 (0-1)
  lastTestTime: number;
  testCount: number;
}

/**
 * 服务器健康状态接口
 */
interface ServerHealth {
  serverId: string;
  isHealthy: boolean;
  lastCheckTime: number;
  consecutiveFailures: number;
  responseTime: number;
}

/**
 * 服务器缓存接口
 */
interface ServerCache {
  serverId: string;
  timestamp: number;
}

/**
 * 服务器评分接口
 */
interface ServerScore {
  server: ServerMetrics;
  score: number;
}

/**
 * 地理位置信息接口
 */
interface GeoLocation {
  country: string;
  region: string;
  city: string;
  latitude: number;
  longitude: number;
}

/**
 * 智能服务器选择服务类
 */
export class ServerSelectionService {
  // 全球测试服务器列表
  private readonly GLOBAL_SERVERS: TestServer[] = [
    {
      id: 'cloudflare-global',
      name: 'Cloudflare Global',
      url: 'https://speed.cloudflare.com',
      location: 'Global CDN',
      latency: 0
    },
    {
      id: 'google-asia',
      name: 'Google Asia',
      url: 'https://www.google.com',
      location: 'Asia Pacific',
      latency: 0
    },
    {
      id: 'alibaba-china',
      name: 'Alibaba Cloud China',
      url: 'https://www.aliyun.com',
      location: 'China',
      latency: 0
    },
    {
      id: 'aws-singapore',
      name: 'AWS Singapore',
      url: 'https://aws.amazon.com',
      location: 'Singapore',
      latency: 0
    },
    {
      id: 'azure-japan',
      name: 'Azure Japan',
      url: 'https://azure.microsoft.com',
      location: 'Japan',
      latency: 0
    }
  ];

  // 服务器性能缓存
  private serverMetrics: Map<string, ServerMetrics> = new Map();
  private serverHealth: Map<string, ServerHealth> = new Map();
  private bestServerCache: ServerCache | null = null;
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存
  private readonly HEALTH_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟健康检查

  /**
   * 选择最佳测试服务器
   */
  async selectBestServer(): Promise<TestServer> {
    console.log('开始智能服务器选择...');

    // 检查缓存
    if (this.bestServerCache && 
        Date.now() - this.bestServerCache.timestamp < this.CACHE_DURATION) {
      const cachedServer = this.GLOBAL_SERVERS.find(s => s.id === this.bestServerCache!.serverId);
      if (cachedServer) {
        console.log(`使用缓存的最佳服务器: ${cachedServer.name}`);
        return cachedServer;
      }
    }

    try {
      // 并行测试所有服务器
      const serverTests = this.GLOBAL_SERVERS.map(server => 
        this.testServerPerformance(server)
      );

      const results = await Promise.allSettled(serverTests);
      const validResults: ServerMetrics[] = [];

      // 收集有效结果
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          validResults.push(result.value);
        } else {
          console.warn(`服务器 ${this.GLOBAL_SERVERS[index].name} 测试失败:`, 
            result.status === 'rejected' ? result.reason : '未知错误');
        }
      });

      if (validResults.length === 0) {
        throw new Error('所有服务器测试都失败');
      }

      // 选择最佳服务器
      const bestServer = this.selectOptimalServer(validResults);
      const serverInfo = this.GLOBAL_SERVERS.find(s => s.id === bestServer.serverId);

      if (!serverInfo) {
        throw new Error('找不到最佳服务器信息');
      }

      // 更新缓存
      const cacheItem: ServerCache = {
        serverId: bestServer.serverId,
        timestamp: Date.now()
      };
      this.bestServerCache = cacheItem;

      console.log(`选择最佳服务器: ${serverInfo.name} (延迟: ${bestServer.latency}ms, 稳定性: ${bestServer.stability})`);
      return serverInfo;

    } catch (error) {
      console.error('服务器选择失败:', error);
      // 返回默认服务器
      return this.GLOBAL_SERVERS[0];
    }
  }

  /**
   * 测试单个服务器性能
   */
  private async testServerPerformance(server: TestServer): Promise<ServerMetrics | null> {
    const testCount = 5;
    const latencies: number[] = [];
    let successCount = 0;

    console.log(`测试服务器性能: ${server.name}`);

    for (let i = 0; i < testCount; i++) {
      try {
        const startTime = Date.now();
        
        const httpRequest = http.createHttp();
        await httpRequest.request(server.url, {
          method: http.RequestMethod.HEAD,
          connectTimeout: 5000,
          readTimeout: 3000,
          header: {
            'User-Agent': 'HarmonyOS-SpeedTest/1.0.0',
            'Cache-Control': 'no-cache'
          }
        });
        
        const latency = Date.now() - startTime;
        latencies.push(latency);
        successCount++;
        
        httpRequest.destroy();
        
        // 测试间隔
        if (i < testCount - 1) {
          await this.delay(200);
        }
      } catch (error) {
        console.warn(`服务器 ${server.name} 第${i+1}次测试失败:`, (error as Error).message);
      }
    }

    if (latencies.length === 0) {
      return null;
    }

    // 计算性能指标
    const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    const stability = this.calculateStability(latencies);
    const loadScore = this.estimateServerLoad(latencies);
    const successRate = successCount / testCount;

    const metrics: ServerMetrics = {
      serverId: server.id,
      latency: avgLatency,
      stability: stability,
      loadScore: loadScore,
      successRate: successRate,
      lastTestTime: Date.now(),
      testCount: latencies.length
    };

    // 更新缓存
    this.serverMetrics.set(server.id, metrics);
    
    // 更新健康状态
    this.updateServerHealth(server.id, successRate > 0.6, avgLatency);

    console.log(`服务器 ${server.name} 性能: 延迟=${avgLatency.toFixed(1)}ms, 稳定性=${stability.toFixed(1)}, 成功率=${(successRate*100).toFixed(1)}%`);
    
    return metrics;
  }

  /**
   * 选择最优服务器
   */
  private selectOptimalServer(metrics: ServerMetrics[]): ServerMetrics {
    // 过滤掉不健康的服务器
    const healthyServers = metrics.filter(m => {
      const health = this.serverHealth.get(m.serverId);
      return !health || health.isHealthy;
    });

    if (healthyServers.length === 0) {
      // 如果没有健康的服务器，选择成功率最高的
      return metrics.reduce((best, current) => 
        current.successRate > best.successRate ? current : best
      );
    }

    // 综合评分算法
    const scoredServers = healthyServers.map(server => {
      // 延迟评分 (越低越好，归一化到0-100)
      const latencyScore = Math.max(0, 100 - server.latency / 2);
      
      // 稳定性评分 (0-100)
      const stabilityScore = server.stability;
      
      // 负载评分 (越低越好)
      const loadScore = 100 - server.loadScore;
      
      // 成功率评分
      const successScore = server.successRate * 100;
      
      // 负载均衡因子 - 避免所有用户选择同一服务器
      const balanceFactor = this.getLoadBalanceFactor(server.serverId);
      
      // 综合评分 (权重: 延迟40%, 稳定性25%, 负载20%, 成功率10%, 均衡5%)
      const totalScore = 
        latencyScore * 0.4 + 
        stabilityScore * 0.25 + 
        loadScore * 0.2 + 
        successScore * 0.1 + 
        balanceFactor * 0.05;

      const serverScore: ServerScore = { server, score: totalScore };
      return serverScore;
    });

    // 选择评分最高的服务器
    const bestScored = scoredServers.reduce((best, current) => 
      current.score > best.score ? current : best
    );

    return bestScored.server;
  }

  /**
   * 计算延迟稳定性
   */
  private calculateStability(latencies: number[]): number {
    if (latencies.length < 2) return 100;

    const mean = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    const variance = latencies.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / latencies.length;
    const stdDev = Math.sqrt(variance);
    
    // 变异系数越小，稳定性越高
    const cv = stdDev / mean;
    return Math.max(0, 100 - cv * 200); // 归一化到0-100
  }

  /**
   * 估算服务器负载
   */
  private estimateServerLoad(latencies: number[]): number {
    // 基于延迟分布估算负载
    const sorted = latencies.sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    const p95 = sorted[Math.floor(sorted.length * 0.95)];
    
    // P95与中位数的比值反映负载情况
    const loadRatio = p95 / median;
    return Math.min(100, (loadRatio - 1) * 50); // 归一化到0-100
  }

  /**
   * 获取负载均衡因子
   */
  private getLoadBalanceFactor(serverId: string): number {
    // 简单的负载均衡：基于服务器ID的哈希值
    const hash = this.simpleHash(serverId + Date.now().toString());
    return 50 + (hash % 50); // 50-100的随机值
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 更新服务器健康状态
   */
  private updateServerHealth(serverId: string, isHealthy: boolean, responseTime: number): void {
    const existing = this.serverHealth.get(serverId);
    
    if (existing) {
      existing.isHealthy = isHealthy;
      existing.lastCheckTime = Date.now();
      existing.responseTime = responseTime;
      existing.consecutiveFailures = isHealthy ? 0 : existing.consecutiveFailures + 1;
    } else {
      this.serverHealth.set(serverId, {
        serverId,
        isHealthy,
        lastCheckTime: Date.now(),
        consecutiveFailures: isHealthy ? 0 : 1,
        responseTime
      });
    }
  }

  /**
   * 获取服务器健康状态
   */
  getServerHealth(serverId: string): ServerHealth | null {
    return this.serverHealth.get(serverId) || null;
  }

  /**
   * 获取所有服务器指标
   */
  getAllServerMetrics(): ServerMetrics[] {
    return Array.from(this.serverMetrics.values());
  }

  /**
   * 清理过期缓存
   */
  cleanupCache(): void {
    const now = Date.now();
    const expireTime = 60 * 60 * 1000; // 1小时

    // 清理过期的服务器指标
    this.serverMetrics.forEach((metrics, serverId) => {
      if (now - metrics.lastTestTime > expireTime) {
        this.serverMetrics.delete(serverId);
      }
    });

    // 清理过期的健康状态
    this.serverHealth.forEach((health, serverId) => {
      if (now - health.lastCheckTime > expireTime) {
        this.serverHealth.delete(serverId);
      }
    });

    console.log('服务器选择缓存已清理');
  }

  /**
   * 延迟工具函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
