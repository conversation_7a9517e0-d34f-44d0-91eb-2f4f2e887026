// 优化后的网络测试功能验证脚本
// 验证基于现代网络协议优化的修复效果

import { SpeedTestService } from './entry/src/main/ets/services/SpeedTestService';
import { NetworkOptimizationConfig } from './entry/src/main/ets/common/config/NetworkOptimizationConfig';
import { TestProgress, TestPhase } from './entry/src/main/ets/common/types/SpeedTestModels';

/**
 * 优化验证主函数
 */
async function main() {
  console.log('=== 优化后的网络测试功能验证 ===');
  console.log('验证基于现代网络协议和最佳实践的优化效果...\n');

  // 验证配置有效性
  if (!NetworkOptimizationConfig.validateConfig()) {
    console.error('❌ 网络优化配置无效');
    return;
  }
  console.log('✅ 网络优化配置验证通过\n');

  const speedTestService = new SpeedTestService();

  try {
    // 1. 连接预热效果验证
    console.log('1. 连接预热效果验证');
    console.log('========================');
    
    await testConnectionWarmupEffect(speedTestService);
    console.log('');

    // 2. TCP慢启动优化验证
    console.log('2. TCP慢启动优化验证');
    console.log('========================');
    
    await testTcpSlowStartOptimization(speedTestService);
    console.log('');

    // 3. 延迟测量准确性验证
    console.log('3. 延迟测量准确性验证');
    console.log('========================');
    
    await testLatencyAccuracy(speedTestService);
    console.log('');

    // 4. 下载速度稳定性验证
    console.log('4. 下载速度稳定性验证');
    console.log('========================');
    
    await testDownloadSpeedStability(speedTestService);
    console.log('');

    // 5. 错误处理改进验证
    console.log('5. 错误处理改进验证');
    console.log('========================');
    
    await testImprovedErrorHandling(speedTestService);
    console.log('');

    // 6. 自适应优化验证
    console.log('6. 自适应优化验证');
    console.log('====================');
    
    await testAdaptiveOptimization(speedTestService);
    console.log('');

    // 7. 性能对比分析
    console.log('7. 性能对比分析');
    console.log('==================');
    
    await performanceComparison(speedTestService);
    console.log('');

    console.log('🎉 优化验证完成！');
    console.log('所有优化措施都已验证，网络测试功能显著改进。');

  } catch (error) {
    console.error('❌ 优化验证过程中发生错误:', error);
  } finally {
    speedTestService.stopTest();
  }
}

/**
 * 测试连接预热效果
 */
async function testConnectionWarmupEffect(service: SpeedTestService) {
  console.log('测试连接预热对延迟测量的影响...');
  
  const iterations = 5;
  const results = {
    withWarmup: [] as number[],
    withoutWarmup: [] as number[]
  };

  // 测试预热效果（通过多次测量观察）
  for (let i = 0; i < iterations; i++) {
    try {
      const latency = await service.testLatency();
      results.withWarmup.push(latency);
      console.log(`第${i+1}次延迟测试: ${latency}ms`);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.warn(`第${i+1}次测试失败:`, error.message);
    }
  }

  if (results.withWarmup.length > 0) {
    const avgLatency = results.withWarmup.reduce((a, b) => a + b, 0) / results.withWarmup.length;
    const stdDev = Math.sqrt(results.withWarmup.reduce((sum, val) => sum + Math.pow(val - avgLatency, 2), 0) / results.withWarmup.length);
    
    console.log(`平均延迟: ${avgLatency.toFixed(1)}ms`);
    console.log(`标准差: ${stdDev.toFixed(1)}ms`);
    console.log(`变异系数: ${(stdDev/avgLatency*100).toFixed(1)}%`);
    
    const isStable = stdDev < avgLatency * 0.2; // 变异系数小于20%认为稳定
    console.log(`连接预热效果: ${isStable ? '✅ 稳定' : '⚠️ 需要改进'}`);
  }
}

/**
 * 测试TCP慢启动优化
 */
async function testTcpSlowStartOptimization(service: SpeedTestService) {
  console.log('测试TCP慢启动优化对下载速度的影响...');
  
  const testSizes = [1, 5, 10]; // MB
  
  for (const size of testSizes) {
    try {
      console.log(`测试 ${size}MB 文件下载...`);
      
      const startTime = Date.now();
      const speed = await service.testDownloadSpeed();
      const testTime = Date.now() - startTime;
      
      console.log(`${size}MB 下载速度: ${speed.toFixed(2)} Mbps`);
      console.log(`测试耗时: ${testTime}ms`);
      
      // 分析是否受到慢启动影响
      const expectedMinSpeed = size >= 5 ? 1.0 : 0.5; // 大文件应该有更高的速度
      const slowStartOptimized = speed > expectedMinSpeed;
      
      console.log(`慢启动优化效果: ${slowStartOptimized ? '✅ 有效' : '⚠️ 可能需要改进'}`);
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.warn(`${size}MB 测试失败:`, error.message);
    }
  }
}

/**
 * 测试延迟测量准确性
 */
async function testLatencyAccuracy(service: SpeedTestService) {
  console.log('测试延迟测量的准确性和一致性...');
  
  const measurements = [];
  const testCount = 10;
  
  for (let i = 0; i < testCount; i++) {
    try {
      const latency = await service.testLatency();
      measurements.push(latency);
      console.log(`测量 ${i+1}: ${latency}ms`);
      
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.warn(`测量 ${i+1} 失败:`, error.message);
    }
  }
  
  if (measurements.length >= 5) {
    const sorted = measurements.sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    const min = Math.min(...measurements);
    const max = Math.max(...measurements);
    const range = max - min;
    
    console.log(`中位数延迟: ${median}ms`);
    console.log(`延迟范围: ${min}ms - ${max}ms (差值: ${range}ms)`);
    
    // 评估测量质量
    const isAccurate = range < median * 0.5; // 范围小于中位数的50%
    console.log(`测量准确性: ${isAccurate ? '✅ 准确' : '⚠️ 波动较大'}`);
    
    // 检查是否在合理范围内
    const mobileRange = NetworkOptimizationConfig.MOBILE_OPTIMIZATION.expectedRttRange;
    const inRange = median >= mobileRange.min && median <= mobileRange.max;
    console.log(`延迟合理性: ${inRange ? '✅ 合理' : '⚠️ 超出预期范围'}`);
  }
}

/**
 * 测试下载速度稳定性
 */
async function testDownloadSpeedStability(service: SpeedTestService) {
  console.log('测试下载速度的稳定性...');
  
  const speeds = [];
  const testCount = 5;
  
  for (let i = 0; i < testCount; i++) {
    try {
      console.log(`下载测试 ${i+1}/${testCount}...`);
      const speed = await service.testDownloadSpeed();
      speeds.push(speed);
      console.log(`速度: ${speed.toFixed(2)} Mbps`);
      
      await new Promise(resolve => setTimeout(resolve, 3000));
    } catch (error) {
      console.warn(`下载测试 ${i+1} 失败:`, error.message);
    }
  }
  
  if (speeds.length >= 3) {
    const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
    const stdDev = Math.sqrt(speeds.reduce((sum, val) => sum + Math.pow(val - avgSpeed, 2), 0) / speeds.length);
    const cv = stdDev / avgSpeed; // 变异系数
    
    console.log(`平均速度: ${avgSpeed.toFixed(2)} Mbps`);
    console.log(`标准差: ${stdDev.toFixed(2)} Mbps`);
    console.log(`变异系数: ${(cv * 100).toFixed(1)}%`);
    
    const isStable = cv < 0.3; // 变异系数小于30%认为稳定
    console.log(`速度稳定性: ${isStable ? '✅ 稳定' : '⚠️ 波动较大'}`);
  }
}

/**
 * 测试改进的错误处理
 */
async function testImprovedErrorHandling(service: SpeedTestService) {
  console.log('测试改进的错误处理机制...');
  
  const testCases = [
    { url: 'https://invalid-domain-12345.com', expectedError: 'ENOTFOUND' },
    { url: 'https://httpstat.us/500', expectedError: 'HTTP 500' },
    { url: 'https://httpstat.us/timeout', expectedError: 'timeout' }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`测试错误处理: ${testCase.url}`);
      await service.testLatency(testCase.url);
      console.log('⚠️ 预期应该失败但成功了');
    } catch (error) {
      console.log(`✅ 正确捕获错误: ${error.message}`);
      
      // 检查错误信息是否友好
      const isFriendly = !error.message.includes('undefined') && 
                        !error.message.includes('[object Object]');
      console.log(`错误信息友好性: ${isFriendly ? '✅ 友好' : '⚠️ 需要改进'}`);
    }
  }
}

/**
 * 测试自适应优化
 */
async function testAdaptiveOptimization(service: SpeedTestService) {
  console.log('测试自适应优化功能...');
  
  try {
    // 执行完整测试以触发自适应优化
    const result = await service.runFullTest((progress: TestProgress) => {
      console.log(`自适应优化进度: ${progress.phase} - ${progress.progress}%`);
    });
    
    console.log('自适应优化结果:');
    console.log(`下载速度: ${result.downloadSpeed.toFixed(2)} Mbps`);
    console.log(`上传速度: ${result.uploadSpeed.toFixed(2)} Mbps`);
    console.log(`延迟: ${result.latency}ms`);
    console.log(`抖动: ${result.jitter.toFixed(2)}ms`);
    
    // 评估自适应效果
    const mobileOptimization = NetworkOptimizationConfig.MOBILE_OPTIMIZATION;
    const latencyInRange = result.latency >= mobileOptimization.expectedRttRange.min && 
                          result.latency <= mobileOptimization.expectedRttRange.max;
    const speedInRange = result.downloadSpeed >= mobileOptimization.expectedBandwidthRange.min && 
                        result.downloadSpeed <= mobileOptimization.expectedBandwidthRange.max;
    
    console.log(`延迟范围检查: ${latencyInRange ? '✅ 合理' : '⚠️ 超出预期'}`);
    console.log(`速度范围检查: ${speedInRange ? '✅ 合理' : '⚠️ 超出预期'}`);
    console.log(`自适应优化: ${latencyInRange && speedInRange ? '✅ 有效' : '⚠️ 需要调整'}`);
    
  } catch (error) {
    console.warn('自适应优化测试失败:', error.message);
  }
}

/**
 * 性能对比分析
 */
async function performanceComparison(service: SpeedTestService) {
  console.log('执行性能对比分析...');
  
  const metrics = {
    testDuration: 0,
    successRate: 0,
    averageLatency: 0,
    averageSpeed: 0,
    errorCount: 0
  };
  
  const startTime = Date.now();
  let successCount = 0;
  let totalTests = 0;
  const latencies = [];
  const speeds = [];
  
  // 执行多轮测试
  for (let round = 1; round <= 3; round++) {
    console.log(`性能测试轮次 ${round}/3`);
    
    try {
      totalTests++;
      const latency = await service.testLatency();
      latencies.push(latency);
      successCount++;
      
      const speed = await service.testDownloadSpeed();
      speeds.push(speed);
      
      console.log(`轮次 ${round}: 延迟=${latency}ms, 速度=${speed.toFixed(2)}Mbps`);
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      metrics.errorCount++;
      console.warn(`轮次 ${round} 失败:`, error.message);
    }
  }
  
  metrics.testDuration = Date.now() - startTime;
  metrics.successRate = successCount / totalTests;
  metrics.averageLatency = latencies.length > 0 ? 
    latencies.reduce((a, b) => a + b, 0) / latencies.length : 0;
  metrics.averageSpeed = speeds.length > 0 ? 
    speeds.reduce((a, b) => a + b, 0) / speeds.length : 0;
  
  console.log('\n性能分析结果:');
  console.log(`总测试时间: ${metrics.testDuration}ms`);
  console.log(`成功率: ${(metrics.successRate * 100).toFixed(1)}%`);
  console.log(`平均延迟: ${metrics.averageLatency.toFixed(1)}ms`);
  console.log(`平均速度: ${metrics.averageSpeed.toFixed(2)}Mbps`);
  console.log(`错误次数: ${metrics.errorCount}`);
  
  // 性能评估
  const thresholds = NetworkOptimizationConfig.PERFORMANCE_MONITORING.thresholds;
  const performanceGood = 
    metrics.successRate >= thresholds.minSuccessRate &&
    metrics.averageLatency <= thresholds.maxLatency &&
    metrics.testDuration <= thresholds.maxDownloadTime;
  
  console.log(`整体性能评估: ${performanceGood ? '✅ 优秀' : '⚠️ 需要优化'}`);
}

// 运行优化验证
main().catch(error => {
  console.error('优化验证脚本执行失败:', error);
});
