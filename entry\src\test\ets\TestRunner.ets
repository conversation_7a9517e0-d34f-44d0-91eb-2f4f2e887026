// 测试运行器配置
// 配置和运行所有单元测试

import { TestRunner, TestCase } from '@ohos/hypium';
import speedTestServiceTest from './services/SpeedTestService.test';
import serverSelectionServiceTest from './services/ServerSelectionService.test';
import concurrentTestServiceTest from './services/ConcurrentTestService.test';

export default class TestRunnerConfig {
  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<void> {
    console.log('开始运行网络测试功能单元测试...');
    
    const testRunner = new TestRunner();
    
    // 注册测试套件
    testRunner.addTestSuite('SpeedTestService测试', speedTestServiceTest);
    testRunner.addTestSuite('ServerSelectionService测试', serverSelectionServiceTest);
    testRunner.addTestSuite('ConcurrentTestService测试', concurrentTestServiceTest);
    
    try {
      const results = await testRunner.run();
      
      console.log('测试完成！');
      console.log(`总测试数: ${results.total}`);
      console.log(`通过: ${results.passed}`);
      console.log(`失败: ${results.failed}`);
      console.log(`跳过: ${results.skipped}`);
      
      if (results.failed > 0) {
        console.error('存在测试失败，请检查代码实现');
        results.failures.forEach((failure, index) => {
          console.error(`失败 ${index + 1}: ${failure.testName}`);
          console.error(`错误: ${failure.error}`);
        });
      } else {
        console.log('所有测试通过！网络测试功能修复成功。');
      }
      
    } catch (error) {
      console.error('测试运行失败:', error);
    }
  }
  
  /**
   * 运行特定的测试套件
   */
  static async runSpecificTest(testName: string): Promise<void> {
    console.log(`运行特定测试: ${testName}`);
    
    const testRunner = new TestRunner();
    
    switch (testName) {
      case 'latency':
        // 只运行延迟测试
        testRunner.addTestCase('延迟测试', async () => {
          const speedTestService = new SpeedTestService();
          const latency = await speedTestService.testLatency();
          console.log(`延迟测试结果: ${latency}ms`);
        });
        break;
        
      case 'download':
        // 只运行下载测试
        testRunner.addTestCase('下载测试', async () => {
          const speedTestService = new SpeedTestService();
          const speed = await speedTestService.testDownloadSpeed();
          console.log(`下载速度测试结果: ${speed.toFixed(2)} Mbps`);
        });
        break;
        
      case 'jitter':
        // 只运行抖动测试
        testRunner.addTestCase('抖动测试', async () => {
          const speedTestService = new SpeedTestService();
          const jitter = await speedTestService.testJitter();
          console.log(`抖动测试结果: ${jitter.toFixed(2)}ms`);
        });
        break;
        
      default:
        console.error(`未知的测试名称: ${testName}`);
        return;
    }
    
    try {
      await testRunner.run();
    } catch (error) {
      console.error(`测试 ${testName} 运行失败:`, error);
    }
  }
  
  /**
   * 性能测试
   */
  static async runPerformanceTest(): Promise<void> {
    console.log('开始性能测试...');
    
    const speedTestService = new SpeedTestService();
    const iterations = 5;
    const results = {
      latency: [] as number[],
      downloadSpeed: [] as number[],
      jitter: [] as number[]
    };
    
    for (let i = 0; i < iterations; i++) {
      console.log(`性能测试第 ${i + 1}/${iterations} 轮`);
      
      try {
        // 延迟测试
        const startLatency = Date.now();
        const latency = await speedTestService.testLatency();
        const latencyTime = Date.now() - startLatency;
        results.latency.push(latencyTime);
        console.log(`延迟测试耗时: ${latencyTime}ms, 结果: ${latency}ms`);
        
        // 下载速度测试
        const startDownload = Date.now();
        const downloadSpeed = await speedTestService.testDownloadSpeed();
        const downloadTime = Date.now() - startDownload;
        results.downloadSpeed.push(downloadTime);
        console.log(`下载测试耗时: ${downloadTime}ms, 结果: ${downloadSpeed.toFixed(2)} Mbps`);
        
        // 抖动测试
        const startJitter = Date.now();
        const jitter = await speedTestService.testJitter();
        const jitterTime = Date.now() - startJitter;
        results.jitter.push(jitterTime);
        console.log(`抖动测试耗时: ${jitterTime}ms, 结果: ${jitter.toFixed(2)}ms`);
        
        // 间隔
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        console.error(`性能测试第 ${i + 1} 轮失败:`, error);
      }
    }
    
    // 计算平均性能
    const avgLatencyTime = results.latency.reduce((a, b) => a + b, 0) / results.latency.length;
    const avgDownloadTime = results.downloadSpeed.reduce((a, b) => a + b, 0) / results.downloadSpeed.length;
    const avgJitterTime = results.jitter.reduce((a, b) => a + b, 0) / results.jitter.length;
    
    console.log('性能测试结果:');
    console.log(`延迟测试平均耗时: ${avgLatencyTime.toFixed(0)}ms`);
    console.log(`下载测试平均耗时: ${avgDownloadTime.toFixed(0)}ms`);
    console.log(`抖动测试平均耗时: ${avgJitterTime.toFixed(0)}ms`);
  }
}

// 导出测试运行器
export { TestRunnerConfig };
