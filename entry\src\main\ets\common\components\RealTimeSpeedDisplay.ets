// 实时速度显示组件
// 集成仪表盘和数字显示，提供完整的实时速度反馈

import { SpeedGauge } from './SpeedGauge';

/**
 * 实时速度信息接口
 */
interface RealTimeSpeedInfo {
  currentSpeed: number;    // 当前瞬时速度 (Mbps)
  averageSpeed: number;    // 平均速度 (Mbps)
  totalBytes: number;      // 总下载字节数
  elapsedTime: number;     // 已用时间 (ms)
  connectionId: number;    // 连接ID
  progress: number;        // 进度百分比
}

/**
 * 测试阶段枚举
 */
enum TestPhase {
  IDLE = 'idle',
  LATENCY = 'latency',
  DOWNLOAD = 'download',
  UPLOAD = 'upload',
  COMPLETE = 'complete'
}

/**
 * 实时速度显示组件
 */
@Component
export struct RealTimeSpeedDisplay {
  @Prop currentDownloadSpeed: number = 0;
  @Prop currentUploadSpeed: number = 0;
  @Prop averageDownloadSpeed: number = 0;
  @Prop averageUploadSpeed: number = 0;
  @Prop latency: number = 0;
  @Prop jitter: number = 0;
  @Prop testPhase: string = TestPhase.IDLE;
  @Prop isRunning: boolean = false;
  @Prop progress: number = 0;
  @Prop elapsedTime: number = 0;
  @Prop totalBytes: number = 0;
  
  @State private selectedTab: number = 0; // 0: 下载, 1: 上传
  
  build() {
    Column() {
      // 标题栏
      this.buildHeader()
      
      // 主要仪表盘区域
      this.buildMainGauge()
      
      // 切换标签
      this.buildTabBar()
      
      // 详细信息区域
      this.buildDetailInfo()
      
      // 进度条
      if (this.isRunning) {
        this.buildProgressBar()
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#FAFAFA')
    .borderRadius(12)
  }
  
  /**
   * 构建标题栏
   */
  @Builder
  private buildHeader() {
    Row() {
      Text(this.getPhaseTitle())
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
      
      Blank()
      
      if (this.isRunning) {
        Row() {
          LoadingProgress()
            .width(20)
            .height(20)
            .color('#007AFF')
          Text(this.formatElapsedTime())
            .fontSize(14)
            .fontColor('#666666')
            .margin({ left: 8 })
        }
      }
    }
    .width('100%')
    .margin({ bottom: 20 })
  }
  
  /**
   * 构建主要仪表盘
   */
  @Builder
  private buildMainGauge() {
    Stack() {
      // 仪表盘组件
      SpeedGauge({
        currentSpeed: this.selectedTab === 0 ? this.currentDownloadSpeed : this.currentUploadSpeed,
        maxSpeed: 2000,
        isRunning: this.isRunning && (
          (this.selectedTab === 0 && this.testPhase === TestPhase.DOWNLOAD) ||
          (this.selectedTab === 1 && this.testPhase === TestPhase.UPLOAD)
        ),
        testType: this.selectedTab === 0 ? '下载' : '上传'
      })
    }
    .width('100%')
    .height(320)
  }
  
  /**
   * 构建切换标签
   */
  @Builder
  private buildTabBar() {
    Row() {
      // 下载标签
      Button() {
        Column() {
          Text('下载')
            .fontSize(16)
            .fontColor(this.selectedTab === 0 ? '#FFFFFF' : '#666666')
          Text(`${this.formatSpeed(this.currentDownloadSpeed)}`)
            .fontSize(12)
            .fontColor(this.selectedTab === 0 ? '#FFFFFF' : '#999999')
            .margin({ top: 2 })
        }
        .padding({ top: 8, bottom: 8, left: 16, right: 16 })
      }
      .backgroundColor(this.selectedTab === 0 ? '#007AFF' : '#F0F0F0')
      .borderRadius(8)
      .layoutWeight(1)
      .onClick(() => {
        this.selectedTab = 0;
      })
      
      Blank().width(12)
      
      // 上传标签
      Button() {
        Column() {
          Text('上传')
            .fontSize(16)
            .fontColor(this.selectedTab === 1 ? '#FFFFFF' : '#666666')
          Text(`${this.formatSpeed(this.currentUploadSpeed)}`)
            .fontSize(12)
            .fontColor(this.selectedTab === 1 ? '#FFFFFF' : '#999999')
            .margin({ top: 2 })
        }
        .padding({ top: 8, bottom: 8, left: 16, right: 16 })
      }
      .backgroundColor(this.selectedTab === 1 ? '#FF9500' : '#F0F0F0')
      .borderRadius(8)
      .layoutWeight(1)
      .onClick(() => {
        this.selectedTab = 1;
      })
    }
    .width('100%')
    .margin({ top: 20, bottom: 20 })
  }
  
  /**
   * 构建详细信息
   */
  @Builder
  private buildDetailInfo() {
    Column() {
      // 速度信息行
      Row() {
        this.buildInfoItem('瞬时速度', this.formatSpeed(
          this.selectedTab === 0 ? this.currentDownloadSpeed : this.currentUploadSpeed
        ), '#007AFF')
        
        Divider()
          .vertical(true)
          .height(40)
          .color('#E5E5EA')
        
        this.buildInfoItem('平均速度', this.formatSpeed(
          this.selectedTab === 0 ? this.averageDownloadSpeed : this.averageUploadSpeed
        ), '#34C759')
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceEvenly)
      
      Divider()
        .color('#E5E5EA')
        .margin({ top: 16, bottom: 16 })
      
      // 网络质量信息行
      Row() {
        this.buildInfoItem('延迟', `${this.latency.toFixed(0)} ms`, '#FF9500')
        
        Divider()
          .vertical(true)
          .height(40)
          .color('#E5E5EA')
        
        this.buildInfoItem('抖动', `${this.jitter.toFixed(1)} ms`, '#FF3B30')
        
        Divider()
          .vertical(true)
          .height(40)
          .color('#E5E5EA')
        
        this.buildInfoItem('数据量', this.formatBytes(this.totalBytes), '#666666')
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceEvenly)
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#FFFFFF')
    .borderRadius(8)
  }
  
  /**
   * 构建信息项
   */
  @Builder
  private buildInfoItem(label: string, value: string, color: string) {
    Column() {
      Text(value)
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor(color)
      Text(label)
        .fontSize(12)
        .fontColor('#999999')
        .margin({ top: 4 })
    }
    .justifyContent(FlexAlign.Center)
    .layoutWeight(1)
  }
  
  /**
   * 构建进度条
   */
  @Builder
  private buildProgressBar() {
    Column() {
      Row() {
        Text(`测试进度: ${this.progress.toFixed(1)}%`)
          .fontSize(14)
          .fontColor('#666666')
        
        Blank()
        
        Text(this.getPhaseDescription())
          .fontSize(12)
          .fontColor('#999999')
      }
      .width('100%')
      .margin({ bottom: 8 })
      
      Progress({
        value: this.progress,
        total: 100,
        type: ProgressType.Linear
      })
        .width('100%')
        .height(6)
        .color('#007AFF')
        .backgroundColor('#F0F0F0')
    }
    .width('100%')
    .margin({ top: 16 })
    .padding(16)
    .backgroundColor('#FFFFFF')
    .borderRadius(8)
  }
  
  /**
   * 获取阶段标题
   */
  private getPhaseTitle(): string {
    switch (this.testPhase) {
      case TestPhase.LATENCY:
        return '延迟测试';
      case TestPhase.DOWNLOAD:
        return '下载测试';
      case TestPhase.UPLOAD:
        return '上传测试';
      case TestPhase.COMPLETE:
        return '测试完成';
      default:
        return '网络测速';
    }
  }
  
  /**
   * 获取阶段描述
   */
  private getPhaseDescription(): string {
    switch (this.testPhase) {
      case TestPhase.LATENCY:
        return '正在测试网络延迟...';
      case TestPhase.DOWNLOAD:
        return '正在测试下载速度...';
      case TestPhase.UPLOAD:
        return '正在测试上传速度...';
      case TestPhase.COMPLETE:
        return '所有测试已完成';
      default:
        return '准备开始测试';
    }
  }
  
  /**
   * 格式化速度显示
   */
  private formatSpeed(speed: number): string {
    if (speed >= 1000) {
      return `${(speed / 1000).toFixed(2)} Gbps`;
    }
    return `${speed.toFixed(1)} Mbps`;
  }
  
  /**
   * 格式化字节数显示
   */
  private formatBytes(bytes: number): string {
    if (bytes >= 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    } else if (bytes >= 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    } else if (bytes >= 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    }
    return `${bytes} B`;
  }
  
  /**
   * 格式化已用时间
   */
  private formatElapsedTime(): string {
    const seconds = Math.floor(this.elapsedTime / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${remainingSeconds}s`;
  }
}
