// 核心测速服务
// 遵循ArkTS规范，实现网速测试功能

import http from '@ohos.net.http';
import {
  SpeedTestResult,
  TestProgress,
  TestStatus,
  TestPhase,
  ValidationResult,
  TestServer,
  TestReliability,
  NetworkEnvironment
} from '../common/types/SpeedTestModels';
import { AppConfig } from '../common/config/AppConfig';

/**
 * 快速测试结果接口
 */
interface QuickTestResult {
  download: number;
  latency: number;
}

/**
 * 测试URL接口
 */
interface TestUrls {
  download: string;
  upload: string;
  latency: string;
}

/**
 * 超时配置接口
 */
interface TimeoutConfig {
  LATENCY: number;
  DOWNLOAD: number;
  UPLOAD: number;
  JITTER: number;
}

/**
 * HTTP请求选项接口
 */
interface HttpRequestOptions {
  method: http.RequestMethod;
  header?: Record<string, string>;
  extraData?: string | ArrayBuffer;
  expectDataType?: http.HttpDataType;
  connectTimeout?: number;
  readTimeout?: number;
}

/**
 * 缓存项接口
 */
interface CacheItem {
  value: number;
  timestamp: number;
}

/**
 * 错误处理工具类
 */
class ErrorHandler {
  /**
   * 处理网络错误
   */
  static handleNetworkError(error: Error, testType: string): Error {
    let userFriendlyMessage = '';
    const originalMessage = error.message;

    // 根据错误类型提供用户友好的错误信息
    if (originalMessage.includes('exceeds the maximum limit')) {
      userFriendlyMessage = `${testType}测试失败: 测试文件过大，请稍后重试`;
    } else if (originalMessage.includes('Failed writing received data')) {
      userFriendlyMessage = `${testType}测试失败: 数据传输异常，请检查网络连接`;
    } else if (originalMessage.includes('timeout')) {
      userFriendlyMessage = `${testType}测试失败: 网络超时，请检查网络连接`;
    } else if (originalMessage.includes('network')) {
      userFriendlyMessage = `${testType}测试失败: 网络连接异常`;
    } else {
      userFriendlyMessage = `${testType}测试失败: ${originalMessage}`;
    }

    console.error(`${testType}测试错误:`, originalMessage);
    return new Error(userFriendlyMessage);
  }
}

/**
 * 网速测试服务类
 */
export class SpeedTestService {
  private readonly defaultServer = AppConfig.getDefaultServer();
  private readonly TEST_URLS: TestUrls = {
    download: this.defaultServer.downloadUrl,
    upload: this.defaultServer.uploadUrl,
    latency: this.defaultServer.latencyUrl
  };

  private readonly TIMEOUT_CONFIG: TimeoutConfig = {
    LATENCY: AppConfig.DEFAULT_TIMEOUTS.latency,
    DOWNLOAD: AppConfig.DEFAULT_TIMEOUTS.download,
    UPLOAD: AppConfig.DEFAULT_TIMEOUTS.upload,
    JITTER: AppConfig.DEFAULT_TIMEOUTS.jitter
  };

  private readonly DEFAULT_MAX_RETRIES = AppConfig.RETRY_SETTINGS.maxRetries;
  private isTestRunning = false;
  private shouldStopTest = false; // 停止测试标志

  // 简单的内存缓存
  private latencyCache: Map<string, CacheItem> = new Map();
  private readonly CACHE_DURATION = AppConfig.CACHE_SETTINGS.duration;



  /**
   * 执行HTTP请求（基于error.log分析优化的重试机制）
   */
  private async executeRequest(url: string, options: HttpRequestOptions, maxRetries: number = 2): Promise<http.HttpResponse> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      const httpRequest = http.createHttp();
      try {
        // 基于error.log分析，优化请求配置
        const optimizedOptions = this.optimizeRequestOptions(options, attempt);

        const response = await httpRequest.request(url, optimizedOptions);

        // 验证响应
        if (this.isValidResponse(response)) {
          return response;
        } else {
          throw new Error('响应数据无效');
        }
      } catch (error) {
        lastError = error as Error;
        const errorMessage = lastError.message;

        console.warn(`请求失败，尝试 ${attempt + 1}/${maxRetries + 1}: ${errorMessage}`);

        // 根据错误类型决定是否重试
        if (!this.shouldRetry(errorMessage, attempt, maxRetries)) {
          throw lastError;
        }

        // 如果不是最后一次尝试，等待一段时间再重试
        if (attempt < maxRetries) {
          const delay = this.calculateRetryDelay(attempt, errorMessage);
          await this.delay(delay);
        }
      } finally {
        httpRequest.destroy();
      }
    }

    // 所有重试都失败了，抛出最后一个错误
    throw lastError || new Error('请求失败');
  }

  /**
   * 优化请求选项
   */
  private optimizeRequestOptions(options: HttpRequestOptions, attempt: number): HttpRequestOptions {
    const optimized = { ...options };

    // 基于error.log分析，动态调整超时时间
    if (attempt > 0) {
      // 重试时增加超时时间
      if (optimized.connectTimeout) {
        optimized.connectTimeout = Math.min(optimized.connectTimeout * (1 + attempt * 0.5), 60000);
      }
      if (optimized.readTimeout) {
        optimized.readTimeout = Math.min(optimized.readTimeout * (1 + attempt * 0.5), 120000);
      }
    }

    // 添加用户代理和其他头部
    optimized.header = {
      'User-Agent': 'HarmonyOS-SpeedTest/1.0.0',
      'Accept': '*/*',
      'Cache-Control': 'no-cache',
      ...optimized.header
    };

    return optimized;
  }

  /**
   * 验证响应是否有效
   */
  private isValidResponse(response: http.HttpResponse): boolean {
    if (!response) {
      return false;
    }

    // 检查响应码
    if (response.responseCode < 200 || response.responseCode >= 400) {
      return false;
    }

    return true;
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(errorMessage: string, attempt: number, maxRetries: number): boolean {
    if (attempt >= maxRetries) {
      return false;
    }

    // 基于error.log分析，某些错误不应该重试
    const nonRetryableErrors = [
      'exceeds the maximum limit',
      'SSL',
      'certificate',
      'authentication'
    ];

    for (const nonRetryable of nonRetryableErrors) {
      if (errorMessage.includes(nonRetryable)) {
        return false;
      }
    }

    // 超时和网络错误可以重试
    const retryableErrors = [
      'timeout',
      'Timeout',
      'network',
      'connection',
      'CURL'
    ];

    for (const retryable of retryableErrors) {
      if (errorMessage.includes(retryable)) {
        return true;
      }
    }

    return true; // 默认重试
  }

  /**
   * 计算重试延迟时间
   */
  private calculateRetryDelay(attempt: number, errorMessage: string): number {
    let baseDelay = 1000; // 1秒基础延迟

    // 根据错误类型调整延迟
    if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
      baseDelay = 2000; // 超时错误延迟更长
    }

    // 指数退避
    return baseDelay * Math.pow(2, attempt);
  }

  /**
   * 测试延迟（精确测量 - 基于error.log分析优化）
   */
  async testLatency(url?: string, useCache: boolean = false): Promise<number> {
    const testUrl = url || this.TEST_URLS.latency;
    const cacheKey = `latency_${testUrl}`;

    // 检查缓存
    if (useCache) {
      const cachedLatency = this.getCachedValue(cacheKey);
      if (cachedLatency !== null) {
        console.log(`使用缓存的延迟值: ${cachedLatency}ms`);
        return cachedLatency;
      }
    }

    // 增加测量次数以提高准确性
    const measurements: number[] = [];
    const testCount = 10; // 增加到10次测量
    let successCount = 0;

    try {
      for (let i = 0; i < testCount; i++) {
        if (this.shouldStopTest) {
          break;
        }

        try {
          const latency = await this.performSingleLatencyTest(testUrl);
          if (this.isValidLatency(latency)) {
            measurements.push(latency);
            successCount++;
            console.log(`延迟测试第${i+1}次: ${latency.toFixed(1)}ms`);
          } else {
            console.warn(`延迟测试第${i+1}次结果异常: ${latency.toFixed(1)}ms`);
          }
        } catch (error) {
          console.warn(`延迟测试第${i+1}次失败: ${(error as Error).message}`);
        }

        // 测量间隔，避免服务器限流
        if (i < testCount - 1) {
          await this.delay(100); // 增加间隔时间
        }
      }

      if (measurements.length < 3) {
        throw new Error(`延迟测试有效样本不足 (${measurements.length}/${testCount})`);
      }

      // 使用改进的统计算法
      const finalLatency = this.calculateOptimalLatency(measurements);
      const roundedLatency = Math.round(finalLatency);

      // 缓存结果
      if (useCache) {
        this.setCacheValue(cacheKey, roundedLatency);
      }

      console.log(`延迟测试完成: ${roundedLatency}ms (${measurements.length}次有效测量)`);
      return roundedLatency;
    } catch (error) {
      throw ErrorHandler.handleNetworkError(error as Error, '延迟');
    }
  }

  /**
   * 执行单次延迟测试
   */
  private async performSingleLatencyTest(testUrl: string): Promise<number> {
    // 使用高精度时间测量
    const startTime = performance.now();

    const requestOptions: HttpRequestOptions = {
      method: http.RequestMethod.HEAD,
      connectTimeout: this.TIMEOUT_CONFIG.LATENCY,
      readTimeout: this.TIMEOUT_CONFIG.LATENCY
    };

    await this.executeRequest(testUrl, requestOptions);

    const endTime = performance.now();
    return endTime - startTime;
  }

  /**
   * 验证延迟值是否合理
   */
  private isValidLatency(latency: number): boolean {
    // 基于error.log分析的合理范围
    if (latency <= 0 || latency > 10000) { // 0-10秒
      return false;
    }

    // 过低的延迟可能是测试错误
    if (latency < 1) {
      return false;
    }

    return true;
  }

  /**
   * 计算最优延迟值（改进的统计算法）
   */
  private calculateOptimalLatency(measurements: number[]): number {
    if (measurements.length === 0) {
      throw new Error('没有有效的延迟测量数据');
    }

    // 排序
    const sorted = measurements.sort((a, b) => a - b);

    // 移除异常值（使用四分位数方法）
    const q1Index = Math.floor(sorted.length * 0.25);
    const q3Index = Math.floor(sorted.length * 0.75);
    const q1 = sorted[q1Index];
    const q3 = sorted[q3Index];
    const iqr = q3 - q1;

    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;

    const filtered = sorted.filter(val => val >= lowerBound && val <= upperBound);

    if (filtered.length === 0) {
      // 如果所有值都被过滤，使用中位数
      return sorted[Math.floor(sorted.length / 2)];
    }

    // 使用过滤后数据的最小值（最接近真实网络延迟）
    const minLatency = Math.min(...filtered);
    const avgLatency = filtered.reduce((sum, val) => sum + val, 0) / filtered.length;

    // 如果最小值和平均值差异不大，使用最小值；否则使用平均值
    return (avgLatency - minLatency) <= minLatency * 0.3 ? minLatency : avgLatency;
  }

  /**
   * 测试下载速度（多级降级机制）
   */
  async testDownloadSpeed(url?: string): Promise<number> {
    const testUrl = url || this.TEST_URLS.download;

    // 多级降级URL策略 - 从大到小
    const fallbackUrls = [
      testUrl,                                                    // 2MB (默认)
      'https://speed.cloudflare.com/__down?bytes=500000',        // 500KB
      'https://speed.cloudflare.com/__down?bytes=100000'         // 100KB
    ];

    let lastError: Error | null = null;

    for (let i = 0; i < fallbackUrls.length; i++) {
      try {
        const result = await this.performDownloadTest(fallbackUrls[i]);

        if (i > 0) {
          const sizeKB = this.extractFileSizeFromUrl(fallbackUrls[i]);
          console.log(`下载测试降级成功，使用 ${sizeKB}KB 文件`);
        }

        return result;
      } catch (error) {
        lastError = error as Error;
        const errorMessage = lastError.message;

        // 如果不是大小相关错误，直接抛出
        if (!errorMessage.includes('exceeds the maximum limit') &&
            !errorMessage.includes('Failed writing received data')) {
          throw ErrorHandler.handleNetworkError(lastError, '下载');
        }

        // 如果是最后一次尝试，抛出错误
        if (i === fallbackUrls.length - 1) {
          console.error('所有下载测试降级都失败');
          throw ErrorHandler.handleNetworkError(lastError, '下载');
        }

        console.warn(`下载测试失败，尝试更小文件: ${errorMessage}`);
      }
    }

    throw ErrorHandler.handleNetworkError(lastError || new Error('未知错误'), '下载');
  }

  /**
   * 从URL中提取文件大小（KB）
   */
  private extractFileSizeFromUrl(url: string): number {
    const match = url.match(/bytes=(\d+)/);
    if (match) {
      return Math.round(parseInt(match[1]) / 1024);
    }
    return 0;
  }

  /**
   * 执行下载测试的核心逻辑（基于error.log分析优化）
   */
  private async performDownloadTest(testUrl: string): Promise<number> {
    // 根据error.log分析，调整测试策略
    const sampleCount = 3;
    const speeds: number[] = [];

    // 动态调整测试文件大小，避免超时
    let optimizedUrl = testUrl;
    if (testUrl.includes('bytes=')) {
      // 根据网络环境动态调整文件大小
      try {
        const networkEnv = await this.detectNetworkEnvironment();
        const fileSize = Math.min(networkEnv.recommendedFileSize, 10000000); // 最大10MB
        optimizedUrl = testUrl.replace(/bytes=\d+/, `bytes=${fileSize}`);
        console.log(`动态调整下载测试文件大小: ${(fileSize/1024/1024).toFixed(1)}MB`);
      } catch (error) {
        // 网络环境检测失败，使用默认5MB
        optimizedUrl = testUrl.replace(/bytes=\d+/, 'bytes=5000000');
        console.warn('网络环境检测失败，使用默认5MB文件');
      }
    }

    for (let i = 0; i < sampleCount; i++) {
      if (this.shouldStopTest) {
        break;
      }

      try {
        const speed = await this.performSingleDownloadTest(optimizedUrl);

        // 基于error.log分析，更严格的结果验证
        if (this.isValidDownloadSpeed(speed)) {
          speeds.push(speed);
          console.log(`下载测试第${i+1}次: ${speed.toFixed(2)} Mbps`);
        } else {
          console.warn(`下载速度异常 (${speed.toFixed(2)} Mbps)，跳过此次采样`);
        }

        // 采样间隔，避免服务器限流
        if (i < sampleCount - 1) {
          await this.delay(500);
        }
      } catch (error) {
        console.warn(`下载测试第${i+1}次采样失败: ${(error as Error).message}`);

        // 如果是超时错误，尝试使用更小的文件
        if ((error as Error).message.includes('timeout') ||
            (error as Error).message.includes('Timeout')) {
          const currentSize = parseInt(optimizedUrl.match(/bytes=(\d+)/)?.[1] || '5000000');
          const smallerSize = Math.max(1000000, Math.floor(currentSize / 2));
          optimizedUrl = optimizedUrl.replace(/bytes=\d+/, `bytes=${smallerSize}`);
          console.log(`检测到超时，降级到 ${(smallerSize/1024/1024).toFixed(1)}MB 文件`);

          // 重试当前测试
          try {
            const speed = await this.performSingleDownloadTest(optimizedUrl);
            if (this.isValidDownloadSpeed(speed)) {
              speeds.push(speed);
              console.log(`降级测试成功: ${speed.toFixed(2)} Mbps`);
            }
          } catch (retryError) {
            console.warn('降级测试也失败:', (retryError as Error).message);
          }
        }

        // 如果是文件过大错误，尝试降级
        if ((error as Error).message.includes('exceeds the maximum limit')) {
          try {
            const fallbackUrl = testUrl.replace(/bytes=\d+/, 'bytes=2000000'); // 降级到2MB
            const speed = await this.performSingleDownloadTest(fallbackUrl);
            if (this.isValidDownloadSpeed(speed)) {
              speeds.push(speed);
              console.log(`下载测试降级成功: ${speed.toFixed(2)} Mbps (2MB文件)`);
            }
          } catch (fallbackError) {
            console.warn('降级下载测试也失败:', (fallbackError as Error).message);
          }
        }
      }
    }

    if (speeds.length === 0) {
      throw new Error('所有下载测试采样都失败');
    }

    // 使用中位数而非平均值，更抗异常值干扰
    const sortedSpeeds = speeds.sort((a, b) => a - b);
    const medianSpeed = sortedSpeeds.length % 2 === 0 ?
      (sortedSpeeds[sortedSpeeds.length / 2 - 1] + sortedSpeeds[sortedSpeeds.length / 2]) / 2 :
      sortedSpeeds[Math.floor(sortedSpeeds.length / 2)];

    console.log(`下载测试完成: ${medianSpeed.toFixed(2)} Mbps (${speeds.length}/${sampleCount}个有效样本)`);
    return medianSpeed;
  }

  /**
   * 验证下载速度是否合理
   */
  private isValidDownloadSpeed(speed: number): boolean {
    // 基于error.log分析的合理范围
    if (speed <= 0 || speed > 2000) { // 0-2000 Mbps
      return false;
    }

    // 过低的速度可能是测试错误
    if (speed < 0.1) {
      return false;
    }

    return true;
  }

  /**
   * 执行单次下载测试（基于error.log分析优化）
   */
  private async performSingleDownloadTest(testUrl: string): Promise<number> {
    // 不进行预热，直接测试以获得更真实的结果
    // 预热可能会影响测试准确性，特别是在网络不稳定时

    const startTime = Date.now();
    const requestOptions: HttpRequestOptions = {
      method: http.RequestMethod.GET,
      expectDataType: http.HttpDataType.ARRAY_BUFFER,
      connectTimeout: this.TIMEOUT_CONFIG.DOWNLOAD,
      readTimeout: this.TIMEOUT_CONFIG.DOWNLOAD
    };

    const response = await this.executeRequest(testUrl, requestOptions);
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000; // 转换为秒

    if (response.result && response.result instanceof ArrayBuffer) {
      const bytes = response.result.byteLength;

      // 验证下载的数据量是否符合预期
      const expectedSize = parseInt(testUrl.match(/bytes=(\d+)/)?.[1] || '0');
      if (expectedSize > 0 && Math.abs(bytes - expectedSize) > expectedSize * 0.1) {
        console.warn(`下载数据量异常: 期望${expectedSize}字节，实际${bytes}字节`);
      }

      // 确保最小测试时间，避免连接建立时间影响结果
      const minTestDuration = 1.0; // 增加到1秒
      if (duration < minTestDuration) {
        console.warn(`单次测试时间过短 (${duration.toFixed(2)}s)，结果可能不准确`);
        // 对于过短的测试，可能需要重新测试或使用更大的文件
        if (duration < 0.5) {
          throw new Error(`测试时间过短 (${duration.toFixed(2)}s)，请使用更大的测试文件`);
        }
      }

      // 修正速度计算公式，确保单位正确
      const speedMbps = (bytes * 8) / (duration * 1000000); // bits per second -> Mbps

      // 验证计算结果的合理性
      if (speedMbps <= 0 || !isFinite(speedMbps)) {
        throw new Error(`速度计算结果异常: ${speedMbps} Mbps`);
      }

      console.log(`单次下载: ${(bytes/1024/1024).toFixed(2)}MB, 耗时: ${duration.toFixed(2)}s, 速度: ${speedMbps.toFixed(2)}Mbps`);
      return speedMbps;
    }

    throw new Error('下载数据无效或为空');
  }

  /**
   * 测试上传速度（增强版 - 多级降级和准确性验证）
   */
  async testUploadSpeed(url?: string, testDataSize: number = 1024 * 1024): Promise<number> { // 1MB起始
    const testUrl = url || this.TEST_URLS.upload;

    // 多级数据大小降级策略
    const dataSizes = [
      testDataSize,           // 1MB
      512 * 1024,            // 512KB
      256 * 1024,            // 256KB
      100 * 1024             // 100KB 最小
    ];

    for (let i = 0; i < dataSizes.length; i++) {
      try {
        const currentSize = dataSizes[i];
        const result = await this.performUploadTest(testUrl, currentSize);

        if (i > 0) {
          console.log(`上传测试降级成功，使用 ${(currentSize / 1024).toFixed(0)}KB 数据`);
        }

        return result;
      } catch (error) {
        const errorMessage = (error as Error).message;

        // 如果是最后一次尝试或不是大小相关错误，直接抛出
        if (i === dataSizes.length - 1 ||
            !errorMessage.includes('exceeds the maximum limit')) {
          throw ErrorHandler.handleNetworkError(error as Error, '上传');
        }

        console.warn(`上传测试失败，尝试更小数据: ${errorMessage}`);
      }
    }

    throw new Error('所有上传测试尝试都失败');
  }

  /**
   * 执行上传测试的核心逻辑（基于error.log分析优化）
   */
  private async performUploadTest(testUrl: string, testDataSize: number): Promise<number> {
    // 基于error.log分析，确保测试时间足够长
    const minTestDuration = 1.0; // 最少1秒测试时间
    let adjustedDataSize = testDataSize;

    // 根据error.log显示的0.19s问题，动态调整数据大小
    // 预估网络速度，调整数据大小以达到最小测试时间
    const estimatedSpeedMbps = 50; // 基于error.log的43.46 Mbps估算
    const minDataSize = Math.max(testDataSize, (estimatedSpeedMbps * minTestDuration * 1000000) / 8);

    if (minDataSize > testDataSize) {
      adjustedDataSize = Math.min(minDataSize, 5 * 1024 * 1024); // 最大5MB
      console.log(`调整上传数据大小: ${(testDataSize/1024).toFixed(0)}KB → ${(adjustedDataSize/1024).toFixed(0)}KB`);
    }

    const testData = new ArrayBuffer(adjustedDataSize);

    // 预热连接
    try {
      const warmupData = new ArrayBuffer(1024); // 1KB预热
      await this.executeRequest(testUrl, {
        method: http.RequestMethod.POST,
        header: { 'Content-Type': 'application/octet-stream' },
        extraData: warmupData,
        connectTimeout: 3000,
        readTimeout: 3000
      });
      await this.delay(100);
    } catch (error) {
      console.warn('上传测试预热失败:', (error as Error).message);
    }

    // 主测试 - 使用Date.now()
    const startTime = Date.now();

    const requestOptions: HttpRequestOptions = {
      method: http.RequestMethod.POST,
      header: {
        'Content-Type': 'application/octet-stream'
      },
      extraData: testData,
      connectTimeout: this.TIMEOUT_CONFIG.UPLOAD,
      readTimeout: this.TIMEOUT_CONFIG.UPLOAD
    };

    await this.executeRequest(testUrl, requestOptions);
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000; // 转换为秒

    // 检查测试时间
    if (duration < minTestDuration) {
      console.warn(`上传测试时间仍然过短 (${duration.toFixed(2)}s)，结果可能不准确`);
    }

    const speedMbps = (adjustedDataSize * 8) / (duration * 1000000); // 转换为Mbps
    console.log(`上传测试完成: ${speedMbps.toFixed(2)} Mbps (${(adjustedDataSize/1024).toFixed(0)}KB, ${duration.toFixed(2)}s)`);

    // 合理性检查 - 基于error.log分析调整阈值
    if (speedMbps > 200) { // 降低异常阈值
      console.warn(`上传速度异常高 (${speedMbps.toFixed(2)} Mbps)，可能测试时间过短`);
    }

    return speedMbps;
  }

  /**
   * 测试抖动（基于error.log分析优化）
   */
  async testJitter(url?: string, testCount: number = 15): Promise<number> {
    const testUrl = url || this.TEST_URLS.latency;
    const latencies: number[] = [];
    let successCount = 0;

    try {
      for (let i = 0; i < testCount; i++) {
        if (this.shouldStopTest) {
          break;
        }

        try {
          const latency = await this.performSingleLatencyTest(testUrl);
          if (this.isValidLatency(latency)) {
            latencies.push(latency);
            successCount++;
          } else {
            console.warn(`抖动测试第${i+1}次结果异常: ${latency.toFixed(1)}ms`);
          }
        } catch (error) {
          console.warn(`抖动测试第${i+1}次失败: ${(error as Error).message}`);
        }

        // 测试间隔，确保测量独立性
        if (i < testCount - 1) {
          await this.delay(150); // 增加间隔时间
        }
      }

      // 确保有足够的有效样本
      if (latencies.length < 5) {
        throw new Error(`抖动测试样本不足 (${latencies.length}/${testCount})`);
      }

      // 使用改进的抖动计算算法
      const jitter = this.calculateJitter(latencies);

      console.log(`抖动测试完成: ${jitter.toFixed(2)}ms (${latencies.length}/${testCount}个有效样本)`);

      // 质量评估
      const successRate = successCount / testCount;
      if (successRate < 0.6) {
        console.warn(`抖动测试质量较低，成功率: ${(successRate * 100).toFixed(1)}%`);
      }

      return jitter;
    } catch (error) {
      throw ErrorHandler.handleNetworkError(error as Error, '抖动');
    }
  }

  /**
   * 计算网络抖动（改进算法）
   */
  private calculateJitter(latencies: number[]): number {
    if (latencies.length < 2) {
      return 0;
    }

    // 移除异常值
    const filtered = this.removeLatencyOutliers(latencies);

    if (filtered.length < 2) {
      return 0;
    }

    // 计算连续延迟差值的平均值（RFC 3393标准）
    const delayVariations: number[] = [];
    for (let i = 1; i < filtered.length; i++) {
      delayVariations.push(Math.abs(filtered[i] - filtered[i - 1]));
    }

    // 返回延迟变化的平均值
    const avgVariation = delayVariations.reduce((sum, val) => sum + val, 0) / delayVariations.length;

    return avgVariation;
  }

  /**
   * 移除延迟测量中的异常值
   */
  private removeLatencyOutliers(latencies: number[]): number[] {
    if (latencies.length < 3) {
      return latencies;
    }

    const sorted = latencies.slice().sort((a, b) => a - b);
    const q1Index = Math.floor(sorted.length * 0.25);
    const q3Index = Math.floor(sorted.length * 0.75);
    const q1 = sorted[q1Index];
    const q3 = sorted[q3Index];
    const iqr = q3 - q1;

    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;

    return latencies.filter(val => val >= lowerBound && val <= upperBound);
  }

  /**
   * 移除异常值（改进的四分位数方法）
   */
  private removeOutliers(values: number[]): number[] {
    if (values.length < 3) return values;

    // 使用四分位数方法，比标准差方法更稳健
    const sorted = values.slice().sort((a, b) => a - b);
    const q1Index = Math.floor(sorted.length * 0.25);
    const q3Index = Math.floor(sorted.length * 0.75);
    const q1 = sorted[q1Index];
    const q3 = sorted[q3Index];
    const iqr = q3 - q1;

    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;

    const filtered = values.filter(val => val >= lowerBound && val <= upperBound);

    // 如果过滤后数据太少，回退到原始数据
    return filtered.length >= Math.max(2, values.length * 0.5) ? filtered : values;
  }

  /**
   * 网络环境检测和自适应优化
   */
  private async detectNetworkEnvironment(): Promise<NetworkEnvironment> {
    try {
      // 快速延迟测试评估网络质量
      const quickLatency = await this.quickLatencyTest();

      // 小文件下载测试评估带宽
      const quickSpeed = await this.quickSpeedTest();

      let networkQuality: 'excellent' | 'good' | 'fair' | 'poor' = 'fair';
      let recommendedFileSize = 2000000; // 默认2MB

      // 基于延迟和速度评估网络质量
      if (quickLatency < 50 && quickSpeed > 50) {
        networkQuality = 'excellent';
        recommendedFileSize = 10000000; // 10MB
      } else if (quickLatency < 100 && quickSpeed > 20) {
        networkQuality = 'good';
        recommendedFileSize = 5000000; // 5MB
      } else if (quickLatency < 200 && quickSpeed > 5) {
        networkQuality = 'fair';
        recommendedFileSize = 2000000; // 2MB
      } else {
        networkQuality = 'poor';
        recommendedFileSize = 1000000; // 1MB
      }

      console.log(`网络环境检测: 质量=${networkQuality}, 延迟=${quickLatency}ms, 速度=${quickSpeed.toFixed(1)}Mbps`);

      const result: NetworkEnvironment = {
        estimatedBandwidth: quickSpeed,
        recommendedFileSize: recommendedFileSize,
        networkQuality: networkQuality
      };
      return result;
    } catch (error) {
      console.warn('网络环境检测失败:', (error as Error).message);
      const fallbackResult: NetworkEnvironment = {
        estimatedBandwidth: 10,
        recommendedFileSize: 2000000,
        networkQuality: 'fair'
      };
      return fallbackResult;
    }
  }

  /**
   * 快速延迟测试（单次）
   */
  private async quickLatencyTest(): Promise<number> {
    const startTime = Date.now();
    try {
      await this.executeRequest(this.TEST_URLS.latency, {
        method: http.RequestMethod.HEAD,
        connectTimeout: 3000,
        readTimeout: 3000
      });
      return Math.round(Date.now() - startTime);
    } catch (error) {
      return 999; // 返回高延迟值表示网络问题
    }
  }

  /**
   * 快速速度测试（小文件）
   */
  private async quickSpeedTest(): Promise<number> {
    const testUrl = 'https://speed.cloudflare.com/__down?bytes=500000'; // 500KB
    const startTime = Date.now();

    try {
      const response = await this.executeRequest(testUrl, {
        method: http.RequestMethod.GET,
        expectDataType: http.HttpDataType.ARRAY_BUFFER,
        connectTimeout: 5000,
        readTimeout: 5000
      });

      const duration = (Date.now() - startTime) / 1000;
      if (response.result && response.result instanceof ArrayBuffer) {
        const bytes = response.result.byteLength;
        return (bytes * 8) / (duration * 1000000); // Mbps
      }
      return 1; // 返回低速度值
    } catch (error) {
      return 1; // 返回低速度值表示网络问题
    }
  }

  /**
   * 评估测试可信度
   */
  private evaluateTestReliability(result: SpeedTestResult): TestReliability {
    const issues: string[] = [];
    let downloadReliable = true;
    let uploadReliable = true;
    let latencyReliable = true;
    let jitterReliable = true;

    // 下载测试可信度评估
    if (result.downloadSpeed > 1000) {
      downloadReliable = false;
      issues.push('下载速度异常高，可能测试时间过短');
    }
    if (result.downloadSpeed < 0.1) {
      downloadReliable = false;
      issues.push('下载速度异常低，可能网络连接有问题');
    }

    // 上传测试可信度评估
    if (result.uploadSpeed > 500) {
      uploadReliable = false;
      issues.push('上传速度异常高，可能测试时间过短');
    }
    if (result.uploadSpeed < 0.1) {
      uploadReliable = false;
      issues.push('上传速度异常低，可能网络连接有问题');
    }

    // 延迟测试可信度评估
    if (result.latency < 1) {
      latencyReliable = false;
      issues.push('延迟异常低，可能测试不准确');
    }
    if (result.latency > 1000) {
      latencyReliable = false;
      issues.push('延迟异常高，网络连接可能有问题');
    }

    // 抖动测试可信度评估
    if (result.jitter > result.latency) {
      jitterReliable = false;
      issues.push('抖动值大于延迟值，测试结果异常');
    }

    // 计算总体可信度评分
    const reliableCount = [downloadReliable, uploadReliable, latencyReliable, jitterReliable]
      .filter(reliable => reliable).length;
    const overallScore = (reliableCount / 4) * 100;

    return {
      downloadReliable,
      uploadReliable,
      latencyReliable,
      jitterReliable,
      overallScore,
      issues
    };
  }

  /**
   * 计算测试质量评分
   */
  private calculateQualityScore(result: SpeedTestResult): number {
    let score = 100;

    // 测试持续时间评分 (30%)
    const durationScore = Math.min(result.duration / 10, 1) * 30; // 10秒为满分
    score = score - 30 + durationScore;

    // 可信度评分 (40%)
    if (result.reliability) {
      score = score - 40 + (result.reliability.overallScore * 0.4);
    }

    // 完整性评分 (30%)
    let completenessScore = 0;
    if (result.downloadSpeed > 0) completenessScore += 10;
    if (result.uploadSpeed > 0) completenessScore += 10;
    if (result.latency > 0) completenessScore += 5;
    if (result.jitter >= 0) completenessScore += 5;
    score = score - 30 + completenessScore;

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * 生成测试警告信息
   */
  private generateTestWarnings(result: SpeedTestResult): string[] {
    const warnings: string[] = [];

    // 测试时间警告
    if (result.duration < 5) {
      warnings.push('测试时间较短，结果可能不够准确');
    }

    // 速度异常警告
    if (result.downloadSpeed > 500) {
      warnings.push('下载速度异常高，请重新测试确认');
    }
    if (result.uploadSpeed > 200) {
      warnings.push('上传速度异常高，请重新测试确认');
    }

    // 网络质量警告
    if (result.latency > 100) {
      warnings.push('网络延迟较高，可能影响使用体验');
    }
    if (result.jitter > 50) {
      warnings.push('网络抖动较大，连接不够稳定');
    }

    // 添加可信度相关警告
    if (result.reliability && result.reliability.issues.length > 0) {
      warnings.push(...result.reliability.issues);
    }

    return warnings;
  }

  /**
   * 执行完整的速度测试
   */
  async performFullSpeedTest(
    progressCallback?: (progress: TestProgress) => void,
    downloadCallback?: (speed: number) => void,
    uploadCallback?: (speed: number) => void,
    latencyCallback?: (latency: number) => void
  ): Promise<SpeedTestResult> {
    if (this.isTestRunning) {
      throw new Error('测试正在进行中');
    }

    this.isTestRunning = true;
    this.shouldStopTest = false; // 重置停止标志
    const testId = Date.now().toString();
    const startTime = Date.now();

    try {
      const result: SpeedTestResult = {
        id: testId,
        downloadSpeed: 0,
        uploadSpeed: 0,
        latency: 0,
        jitter: 0,
        packetLoss: 0,
        networkInfo: {
          type: 'unknown',
          operator: 'unknown',
          ipAddress: 'unknown',
          signalStrength: 0,
          isConnected: true
        },
        testServer: {
          id: 'cloudflare-1',
          name: 'Cloudflare',
          location: 'Global',
          url: this.TEST_URLS.download,
          latency: 0
        },
        location: 'unknown',
        timestamp: startTime,
        duration: 0
      };

      // 网络环境检测和自适应优化
      console.log('开始网络环境检测...');
      const networkEnv = await this.detectNetworkEnvironment();

      // 根据网络环境调整测试参数
      if (networkEnv.recommendedFileSize !== 2000000) {
        const optimizedUrl = this.TEST_URLS.download.replace(/bytes=\d+/, `bytes=${networkEnv.recommendedFileSize}`);
        console.log(`根据网络环境调整测试文件大小: ${(networkEnv.recommendedFileSize/1024/1024).toFixed(1)}MB`);
        this.TEST_URLS.download = optimizedUrl;
      }

      // 更新结果中的网络信息
      result.networkInfo.type = networkEnv.networkQuality;
      result.networkInfo.signalStrength = networkEnv.estimatedBandwidth;
      result.testServer.name = `Adaptive Server (${networkEnv.networkQuality})`;

      // 延迟测试
      if (progressCallback) {
        progressCallback({
          phase: TestPhase.LATENCY,
          progress: 0,
          currentSpeed: 0,
          message: '正在测试延迟...'
        });
      }

      if (this.shouldStopTest) {
        throw new Error('测试已被用户停止');
      }

      result.latency = await this.testLatency();
      if (latencyCallback) {
        latencyCallback(result.latency);
      }

      // 下载测试
      if (progressCallback) {
        progressCallback({
          phase: TestPhase.DOWNLOAD,
          progress: 25,
          currentSpeed: 0,
          message: '正在测试下载速度...'
        });
      }

      result.downloadSpeed = await this.testDownloadSpeed();
      if (downloadCallback) {
        downloadCallback(result.downloadSpeed);
      }

      // 上传测试
      if (progressCallback) {
        progressCallback({
          phase: TestPhase.UPLOAD,
          progress: 50,
          currentSpeed: 0,
          message: '正在测试上传速度...'
        });
      }

      result.uploadSpeed = await this.testUploadSpeed();
      if (uploadCallback) {
        uploadCallback(result.uploadSpeed);
      }

      // 抖动测试
      if (progressCallback) {
        progressCallback({
          phase: TestPhase.JITTER,
          progress: 75,
          currentSpeed: 0,
          message: '正在测试抖动...'
        });
      }

      result.jitter = await this.testJitter();

      const endTime = Date.now();
      result.duration = endTime - startTime;

      // 添加质量评估
      result.reliability = this.evaluateTestReliability(result);
      result.qualityScore = this.calculateQualityScore(result);
      result.warnings = this.generateTestWarnings(result);

      if (progressCallback) {
        progressCallback({
          phase: TestPhase.COMPLETED,
          progress: 100,
          currentSpeed: 0,
          message: '测试完成'
        });
      }

      console.log('完整测试完成:', result);
      console.log(`测试质量评分: ${result.qualityScore}/100`);
      if (result.warnings && result.warnings.length > 0) {
        console.warn('测试警告:', result.warnings);
      }

      return result;

    } catch (error) {
      console.error('测试失败:', error);
      throw new Error(`测试失败: ${(error as Error).message}`);
    } finally {
      this.isTestRunning = false;
    }
  }

  /**
   * 快速测试（仅下载和延迟）
   */
  async quickTest(): Promise<QuickTestResult> {
    try {
      const latency = await this.testLatency();
      const downloadSpeed = await this.testDownloadSpeed();

      const result: QuickTestResult = {
        download: downloadSpeed,
        latency: latency
      };

      return result;
    } catch (error) {
      console.error('快速测试失败:', error);
      throw new Error(`快速测试失败: ${(error as Error).message}`);
    }
  }

  /**
   * 验证网络连接
   */
  async validateConnection(): Promise<ValidationResult> {
    try {
      await this.testLatency();
      const result: ValidationResult = {
        isValid: true,
        message: '网络连接正常'
      };
      return result;
    } catch (error) {
      const result: ValidationResult = {
        isValid: false,
        message: `网络连接异常: ${(error as Error).message}`
      };
      return result;
    }
  }

  /**
   * 停止测试
   */
  stopTest(): void {
    this.shouldStopTest = true;
    this.isTestRunning = false;
    console.log('测试已停止');
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cacheKey: string): boolean {
    const cached = this.latencyCache.get(cacheKey);
    if (!cached) {
      return false;
    }
    return (Date.now() - cached.timestamp) < this.CACHE_DURATION;
  }

  /**
   * 获取缓存值
   */
  private getCachedValue(cacheKey: string): number | null {
    if (this.isCacheValid(cacheKey)) {
      const cached = this.latencyCache.get(cacheKey);
      return cached ? cached.value : null;
    }
    return null;
  }

  /**
   * 设置缓存值
   */
  private setCacheValue(cacheKey: string, value: number): void {
    const cacheItem: CacheItem = {
      value: value,
      timestamp: Date.now()
    };
    this.latencyCache.set(cacheKey, cacheItem);
  }

  /**
   * 延迟工具方法
   */
  private delay(ms: number): Promise<void> {
    return new Promise<void>((resolve) => {
      setTimeout(() => resolve(), ms);
    });
  }
}