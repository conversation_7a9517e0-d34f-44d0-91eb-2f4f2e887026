// 网络优化配置
// 基于现代网络协议和最佳实践的优化配置

/**
 * HTTP协议版本配置
 */
export interface HttpVersionConfig {
  preferHttp2: boolean;
  preferHttp3: boolean;
  fallbackToHttp1: boolean;
}

/**
 * 连接复用配置
 */
export interface ConnectionReuseConfig {
  enableKeepAlive: boolean;
  maxConnectionsPerHost: number;
  connectionTimeout: number;
  keepAliveTimeout: number;
}

/**
 * TCP优化配置
 */
export interface TcpOptimizationConfig {
  enableTcpFastOpen: boolean;
  tcpNoDelay: boolean;
  socketBufferSize: number;
  congestionControl: 'cubic' | 'bbr' | 'reno';
}

/**
 * 测试优化配置
 */
export interface TestOptimizationConfig {
  warmupEnabled: boolean;
  warmupRequests: number;
  slowStartWarmup: boolean;
  parallelConnections: number;
  adaptiveFileSize: boolean;
}

/**
 * 网络优化配置类
 */
export class NetworkOptimizationConfig {
  /**
   * HTTP协议版本配置
   */
  static readonly HTTP_VERSION: HttpVersionConfig = {
    preferHttp2: true,
    preferHttp3: false, // HarmonyOS可能还不完全支持HTTP/3
    fallbackToHttp1: true
  };

  /**
   * 连接复用配置
   */
  static readonly CONNECTION_REUSE: ConnectionReuseConfig = {
    enableKeepAlive: true,
    maxConnectionsPerHost: 6, // 遵循HTTP/1.1标准
    connectionTimeout: 30000, // 30秒
    keepAliveTimeout: 60000   // 60秒
  };

  /**
   * TCP优化配置
   */
  static readonly TCP_OPTIMIZATION: TcpOptimizationConfig = {
    enableTcpFastOpen: true,
    tcpNoDelay: true,
    socketBufferSize: 65536, // 64KB
    congestionControl: 'cubic' // 默认使用CUBIC
  };

  /**
   * 测试优化配置
   */
  static readonly TEST_OPTIMIZATION: TestOptimizationConfig = {
    warmupEnabled: true,
    warmupRequests: 3,
    slowStartWarmup: true,
    parallelConnections: 1, // 移动网络建议单连接
    adaptiveFileSize: true
  };

  /**
   * 移动网络特定优化
   */
  static readonly MOBILE_OPTIMIZATION = {
    // 移动网络的RTT通常较高
    expectedRttRange: {
      min: 20,   // 20ms (优秀的4G/5G)
      max: 500,  // 500ms (较差的网络)
      typical: 100 // 100ms (典型4G网络)
    },

    // 移动网络的带宽变化较大
    expectedBandwidthRange: {
      min: 1,     // 1 Mbps (2G/3G)
      max: 1000,  // 1 Gbps (5G)
      typical: 50 // 50 Mbps (典型4G网络)
    },

    // 移动网络的抖动通常较高
    expectedJitterRange: {
      min: 5,     // 5ms (稳定网络)
      max: 200,   // 200ms (不稳定网络)
      typical: 30 // 30ms (典型移动网络)
    },

    // 移动网络优化策略
    strategies: {
      useAdaptiveTimeout: true,
      enableNetworkTypeDetection: true,
      optimizeForBatteryLife: true,
      reduceBackgroundActivity: true
    }
  };

  /**
   * 测试文件大小策略
   */
  static readonly FILE_SIZE_STRATEGY = {
    // 基于网络质量的文件大小选择
    excellent: {
      latencyThreshold: 50,    // <50ms
      bandwidthThreshold: 100, // >100Mbps
      downloadSize: 25 * 1024 * 1024, // 25MB
      uploadSize: 10 * 1024 * 1024     // 10MB
    },
    good: {
      latencyThreshold: 100,   // 50-100ms
      bandwidthThreshold: 50,  // 50-100Mbps
      downloadSize: 10 * 1024 * 1024, // 10MB
      uploadSize: 5 * 1024 * 1024      // 5MB
    },
    fair: {
      latencyThreshold: 200,   // 100-200ms
      bandwidthThreshold: 20,  // 20-50Mbps
      downloadSize: 5 * 1024 * 1024,  // 5MB
      uploadSize: 2 * 1024 * 1024      // 2MB
    },
    poor: {
      latencyThreshold: Infinity, // >200ms
      bandwidthThreshold: 0,      // <20Mbps
      downloadSize: 2 * 1024 * 1024,  // 2MB
      uploadSize: 1 * 1024 * 1024      // 1MB
    }
  };

  /**
   * 错误处理策略
   */
  static readonly ERROR_HANDLING = {
    // 可重试的错误类型
    retryableErrors: [
      'timeout',
      'ETIMEDOUT',
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'network error',
      'connection error'
    ],

    // 不可重试的错误类型
    nonRetryableErrors: [
      'SSL',
      'certificate',
      'authentication',
      'authorization',
      'exceeds the maximum limit',
      'file too large'
    ],

    // 重试策略
    retryStrategy: {
      maxRetries: 3,
      baseDelay: 1000,      // 1秒
      maxDelay: 10000,      // 10秒
      backoffMultiplier: 2, // 指数退避
      jitter: true          // 添加随机抖动
    }
  };

  /**
   * 性能监控配置
   */
  static readonly PERFORMANCE_MONITORING = {
    enableDetailedLogging: true,
    logNetworkTimings: true,
    trackConnectionReuse: true,
    monitorMemoryUsage: true,
    
    // 性能指标阈值
    thresholds: {
      maxLatency: 1000,        // 1秒
      maxDownloadTime: 60000,  // 60秒
      maxUploadTime: 45000,    // 45秒
      maxJitter: 500,          // 500ms
      minSuccessRate: 0.8      // 80%成功率
    }
  };

  /**
   * 获取基于网络类型的优化配置
   */
  static getOptimizationForNetworkType(networkType: string): any {
    switch (networkType.toLowerCase()) {
      case 'wifi':
        return {
          ...this.TEST_OPTIMIZATION,
          parallelConnections: 2, // WiFi可以使用更多连接
          warmupRequests: 2       // 减少预热请求
        };
      
      case '5g':
        return {
          ...this.TEST_OPTIMIZATION,
          parallelConnections: 2,
          slowStartWarmup: false  // 5G网络通常不需要慢启动预热
        };
      
      case '4g':
        return {
          ...this.TEST_OPTIMIZATION,
          parallelConnections: 1,
          warmupRequests: 3
        };
      
      case '3g':
      case '2g':
        return {
          ...this.TEST_OPTIMIZATION,
          parallelConnections: 1,
          warmupRequests: 4,      // 增加预热请求
          slowStartWarmup: true
        };
      
      default:
        return this.TEST_OPTIMIZATION;
    }
  }

  /**
   * 获取基于延迟的文件大小
   */
  static getFileSizeForLatency(latency: number, bandwidth: number): { download: number; upload: number } {
    const strategies = this.FILE_SIZE_STRATEGY;
    
    if (latency < strategies.excellent.latencyThreshold && bandwidth > strategies.excellent.bandwidthThreshold) {
      return { download: strategies.excellent.downloadSize, upload: strategies.excellent.uploadSize };
    } else if (latency < strategies.good.latencyThreshold && bandwidth > strategies.good.bandwidthThreshold) {
      return { download: strategies.good.downloadSize, upload: strategies.good.uploadSize };
    } else if (latency < strategies.fair.latencyThreshold && bandwidth > strategies.fair.bandwidthThreshold) {
      return { download: strategies.fair.downloadSize, upload: strategies.fair.uploadSize };
    } else {
      return { download: strategies.poor.downloadSize, upload: strategies.poor.uploadSize };
    }
  }

  /**
   * 验证配置有效性
   */
  static validateConfig(): boolean {
    try {
      // 验证超时配置
      if (this.CONNECTION_REUSE.connectionTimeout <= 0 || this.CONNECTION_REUSE.keepAliveTimeout <= 0) {
        return false;
      }

      // 验证文件大小配置
      const strategies = Object.values(this.FILE_SIZE_STRATEGY);
      for (const strategy of strategies) {
        if (strategy.downloadSize <= 0 || strategy.uploadSize <= 0) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('网络优化配置验证失败:', error);
      return false;
    }
  }
}
