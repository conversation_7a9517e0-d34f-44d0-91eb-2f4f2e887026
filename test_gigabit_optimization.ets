// 千兆网络优化效果验证脚本
// 测试优化后的测速应用是否能够准确测量千兆网络的真实速度

import { SpeedTestService } from './entry/src/main/ets/services/SpeedTestService';
import { ConcurrentTestService } from './entry/src/main/ets/services/ConcurrentTestService';
import { NetworkService } from './entry/src/main/ets/services/NetworkService';
import { TestProgress, NetworkType } from './entry/src/main/ets/common/types/SpeedTestModels';

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始千兆网络优化效果验证...\n');
  
  const speedTestService = new SpeedTestService();
  const concurrentTestService = new ConcurrentTestService();
  const networkService = new NetworkService();
  
  try {
    // 1. 网络环境检测
    await testNetworkDetection(networkService);
    
    // 2. 并发连接优化测试
    await testConcurrentOptimization(concurrentTestService);
    
    // 3. 实时速度显示测试
    await testRealTimeSpeedDisplay(speedTestService);
    
    // 4. 完整测速性能测试
    await testFullSpeedTestPerformance(speedTestService);
    
    // 5. 高速网络适配测试
    await testHighSpeedNetworkAdaptation(speedTestService);
    
    console.log('\n✅ 千兆网络优化验证完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

/**
 * 测试网络检测功能
 */
async function testNetworkDetection(networkService: NetworkService) {
  console.log('📡 测试网络检测功能...');
  
  try {
    const networkInfo = await networkService.getNetworkInfo();
    console.log(`网络类型: ${networkInfo.type}`);
    console.log(`IP地址: ${networkInfo.ipAddress}`);
    console.log(`连接状态: ${networkInfo.isConnected ? '已连接' : '未连接'}`);
    
    const isConnected = await networkService.checkConnectivity();
    console.log(`连接检查: ${isConnected ? '✅ 正常' : '❌ 异常'}`);
    
    console.log('✅ 网络检测功能正常\n');
  } catch (error) {
    console.warn('⚠️ 网络检测失败:', error.message);
  }
}

/**
 * 测试并发连接优化
 */
async function testConcurrentOptimization(concurrentTestService: ConcurrentTestService) {
  console.log('⚡ 测试并发连接优化...');
  
  const testUrl = 'https://speed.cloudflare.com/__down';
  const networkTypes = [NetworkType.WIFI, NetworkType.ETHERNET];
  
  for (const networkType of networkTypes) {
    try {
      console.log(`测试 ${NetworkType[networkType]} 网络...`);
      
      const startTime = Date.now();
      let maxSpeed = 0;
      let totalConnections = 0;
      
      const result = await concurrentTestService.performConcurrentDownloadTest(
        testUrl,
        networkType,
        (progress: number, connectionId: number) => {
          if (progress === 100) {
            totalConnections++;
          }
        },
        (speedInfo) => {
          maxSpeed = Math.max(maxSpeed, speedInfo.currentSpeed);
          if (speedInfo.progress % 25 === 0) {
            console.log(`  实时速度: ${speedInfo.currentSpeed.toFixed(2)} Mbps, 平均: ${speedInfo.averageSpeed.toFixed(2)} Mbps`);
          }
        }
      );
      
      const testTime = Date.now() - startTime;
      
      console.log(`${NetworkType[networkType]} 测试结果:`);
      console.log(`  总速度: ${result.totalSpeed.toFixed(2)} Mbps`);
      console.log(`  最大瞬时速度: ${maxSpeed.toFixed(2)} Mbps`);
      console.log(`  并发连接数: ${result.connectionResults.length}`);
      console.log(`  成功连接数: ${result.successfulConnections}`);
      console.log(`  并发效率: ${(result.efficiency * 100).toFixed(1)}%`);
      console.log(`  测试耗时: ${testTime}ms`);
      
      // 评估优化效果
      const isOptimized = evaluateOptimization(result, networkType);
      console.log(`  优化效果: ${isOptimized ? '✅ 优秀' : '⚠️ 需要改进'}\n`);
      
    } catch (error) {
      console.warn(`⚠️ ${NetworkType[networkType]} 测试失败:`, error.message);
    }
  }
}

/**
 * 测试实时速度显示
 */
async function testRealTimeSpeedDisplay(speedTestService: SpeedTestService) {
  console.log('📊 测试实时速度显示...');
  
  try {
    let speedUpdates = 0;
    let maxDisplayedSpeed = 0;
    
    const result = await speedTestService.performFullSpeedTest(
      (progress: TestProgress) => {
        console.log(`进度: ${progress.phase} - ${progress.progress}%`);
      },
      (downloadSpeed: number) => {
        console.log(`下载速度: ${downloadSpeed.toFixed(2)} Mbps`);
      },
      (uploadSpeed: number) => {
        console.log(`上传速度: ${uploadSpeed.toFixed(2)} Mbps`);
      },
      (latency: number) => {
        console.log(`延迟: ${latency}ms`);
      },
      (speedInfo) => {
        speedUpdates++;
        maxDisplayedSpeed = Math.max(maxDisplayedSpeed, speedInfo.currentSpeed);
        
        if (speedUpdates % 10 === 0) { // 每10次更新显示一次
          console.log(`  实时速度更新 #${speedUpdates}: ${speedInfo.currentSpeed.toFixed(2)} Mbps`);
        }
      }
    );
    
    console.log('实时速度显示测试结果:');
    console.log(`  速度更新次数: ${speedUpdates}`);
    console.log(`  最大显示速度: ${maxDisplayedSpeed.toFixed(2)} Mbps`);
    console.log(`  最终下载速度: ${result.downloadSpeed.toFixed(2)} Mbps`);
    console.log(`  实时显示: ${speedUpdates > 0 ? '✅ 正常' : '❌ 异常'}\n`);
    
  } catch (error) {
    console.warn('⚠️ 实时速度显示测试失败:', error.message);
  }
}

/**
 * 测试完整测速性能
 */
async function testFullSpeedTestPerformance(speedTestService: SpeedTestService) {
  console.log('🏃 测试完整测速性能...');
  
  const testRounds = 3;
  const results = [];
  
  for (let round = 1; round <= testRounds; round++) {
    try {
      console.log(`第 ${round} 轮测试...`);
      
      const startTime = Date.now();
      const result = await speedTestService.performFullSpeedTest(
        (progress: TestProgress) => {
          if (progress.progress % 50 === 0) {
            console.log(`  ${progress.phase}: ${progress.progress}%`);
          }
        }
      );
      const testTime = Date.now() - startTime;
      
      results.push({
        round,
        downloadSpeed: result.downloadSpeed,
        uploadSpeed: result.uploadSpeed,
        latency: result.latency,
        testTime
      });
      
      console.log(`  轮次${round}结果: 下载=${result.downloadSpeed.toFixed(2)}Mbps, 上传=${result.uploadSpeed.toFixed(2)}Mbps, 延迟=${result.latency}ms, 耗时=${testTime}ms`);
      
      if (round < testRounds) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 轮次间延迟
      }
      
    } catch (error) {
      console.warn(`  轮次${round}失败:`, error.message);
    }
  }
  
  // 分析性能稳定性
  if (results.length > 0) {
    const avgDownload = results.reduce((sum, r) => sum + r.downloadSpeed, 0) / results.length;
    const avgUpload = results.reduce((sum, r) => sum + r.uploadSpeed, 0) / results.length;
    const avgLatency = results.reduce((sum, r) => sum + r.latency, 0) / results.length;
    const avgTime = results.reduce((sum, r) => sum + r.testTime, 0) / results.length;
    
    console.log('性能测试汇总:');
    console.log(`  平均下载速度: ${avgDownload.toFixed(2)} Mbps`);
    console.log(`  平均上传速度: ${avgUpload.toFixed(2)} Mbps`);
    console.log(`  平均延迟: ${avgLatency.toFixed(1)} ms`);
    console.log(`  平均测试时间: ${avgTime.toFixed(0)} ms`);
    
    const isHighPerformance = avgDownload > 100 && avgTime < 60000;
    console.log(`  性能评估: ${isHighPerformance ? '✅ 高性能' : '⚠️ 需要优化'}\n`);
  }
}

/**
 * 测试高速网络适配
 */
async function testHighSpeedNetworkAdaptation(speedTestService: SpeedTestService) {
  console.log('🚀 测试高速网络适配...');
  
  try {
    // 模拟高速网络环境测试
    const quickResult = await speedTestService.quickTest();
    
    console.log('高速网络适配测试结果:');
    console.log(`  快速测试下载速度: ${quickResult.download.toFixed(2)} Mbps`);
    console.log(`  快速测试延迟: ${quickResult.latency} ms`);
    
    const isGigabitCapable = quickResult.download > 500;
    console.log(`  千兆网络能力: ${isGigabitCapable ? '✅ 支持' : '⚠️ 受限'}`);
    
    if (isGigabitCapable) {
      console.log('🎉 恭喜！您的网络测速应用已成功优化，能够准确测量千兆网络速度！');
    } else {
      console.log('💡 建议：继续优化并发连接数和测试参数以充分利用千兆带宽');
    }
    
  } catch (error) {
    console.warn('⚠️ 高速网络适配测试失败:', error.message);
  }
}

/**
 * 评估优化效果
 */
function evaluateOptimization(result: any, networkType: NetworkType): boolean {
  const minExpectedSpeed = networkType === NetworkType.ETHERNET ? 200 : 100; // Mbps
  const minConnections = networkType === NetworkType.ETHERNET ? 10 : 8;
  const minEfficiency = 0.6;
  
  return result.totalSpeed >= minExpectedSpeed &&
         result.connectionResults.length >= minConnections &&
         result.efficiency >= minEfficiency;
}

// 运行测试
main().catch(console.error);
