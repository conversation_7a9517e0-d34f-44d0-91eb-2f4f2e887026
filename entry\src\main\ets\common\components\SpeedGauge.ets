// 简化的速度显示组件
// 符合ArkTS规范，提供实时速度可视化

/**
 * 简化的速度表组件
 */
@Component
export struct SpeedGauge {
  @Prop currentSpeed: number = 0;
  @Prop maxSpeed: number = 2000; // 最大刻度2000Mbps
  @Prop isRunning: boolean = false;
  @Prop testType: string = '下载'; // '下载' 或 '上传'

  aboutToAppear() {
    // 组件初始化
  }
  
  build() {
    Stack() {
      // 背景圆环
      Circle()
        .width(200)
        .height(200)
        .fill(Color.Transparent)
        .stroke('#E5E5EA')
        .strokeWidth(12)

      // 进度圆环
      Circle()
        .width(200)
        .height(200)
        .fill(Color.Transparent)
        .stroke(this.getCurrentSpeedColor())
        .strokeWidth(12)
        .strokeDashArray([this.calculateProgress(), 1000])
        .rotate({ angle: -90 })

      // 中心内容
      Column() {
        // 速度数值
        Text(this.formatSpeed(this.currentSpeed))
          .fontSize(36)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
          .textAlign(TextAlign.Center)

        // 测试类型
        Text(this.testType)
          .fontSize(16)
          .fontColor('#666666')
          .margin({ top: 4 })

        // 运行状态
        if (this.isRunning) {
          Row() {
            LoadingProgress()
              .width(16)
              .height(16)
              .color('#007AFF')
            Text('测试中')
              .fontSize(12)
              .fontColor('#007AFF')
              .margin({ left: 4 })
          }
          .margin({ top: 8 })
        }
      }
      .justifyContent(FlexAlign.Center)
      .width(120)
      .height(80)
    }
    .width(320)
    .height(320)
  }
  
  /**
   * 计算进度弧长
   */
  private calculateProgress(): number {
    const progress = Math.min(this.currentSpeed / this.maxSpeed, 1);
    const circumference = 2 * Math.PI * 94; // 半径约94
    return progress * circumference * 0.75; // 显示3/4圆弧
  }

  /**
   * 获取当前速度对应的颜色
   */
  private getCurrentSpeedColor(): string {
    const progress = this.currentSpeed / this.maxSpeed;

    if (progress >= 0.25) {
      return '#FF3B30'; // 红色 - 高速
    } else if (progress >= 0.05) {
      return '#FF9500'; // 橙色 - 中速
    } else {
      return '#34C759'; // 绿色 - 低速
    }
  }

  /**
   * 格式化速度显示
   */
  private formatSpeed(speed: number): string {
    if (speed >= 1000) {
      return `${(speed / 1000).toFixed(2)}G`;
    }
    return `${speed.toFixed(1)}M`;
  }
}
