// 测速仪表盘组件
// 遵循ArkTS规范，显示网速测试结果

/**
 * 测速仪表盘组件
 */
@Component
export struct Speedometer {
  @Prop speed: number = 0;
  @Prop maxSpeed: number = 100;
  @Prop isRunning: boolean = false;
  @Prop unit: string = 'Mbps';
  
  build() {
    Stack() {
      // 背景圆环
      Circle()
        .width(200)
        .height(200)
        .fill(Color.Transparent)
        .stroke('#E5E5EA')
        .strokeWidth(12)
      
      // 进度圆环
      Circle()
        .width(200)
        .height(200)
        .fill(Color.Transparent)
        .stroke(this.getSpeedColor())
        .strokeWidth(12)
        .strokeDashArray([this.calculateProgress(), 1000])
        .rotate({ angle: -90 })
      
      // 中心内容
      Column() {
        // 速度数值
        Text(this.speed.toFixed(1))
          .fontSize(48)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
        
        // 单位
        Text(this.unit)
          .fontSize(16)
          .fontColor('#666666')
          .margin({ top: 4 })
        
        // 状态指示
        if (this.isRunning) {
          Text('测试中...')
            .fontSize(12)
            .fontColor('#007AFF')
            .margin({ top: 8 })
        }
      }
      .justifyContent(FlexAlign.Center)
    }
    .width(220)
    .height(220)
  }
  
  /**
   * 计算进度弧长
   */
  private calculateProgress(): number {
    const progress = Math.min(this.speed / this.maxSpeed, 1);
    const circumference = 2 * Math.PI * 94; // 半径约94
    return progress * circumference * 0.75; // 显示3/4圆弧
  }
  
  /**
   * 获取速度对应的颜色
   */
  private getSpeedColor(): string {
    const progress = this.speed / this.maxSpeed;
    
    if (progress >= 0.8) {
      return '#34C759'; // 绿色 - 优秀
    } else if (progress >= 0.6) {
      return '#007AFF'; // 蓝色 - 良好
    } else if (progress >= 0.4) {
      return '#FF9500'; // 橙色 - 一般
    } else if (progress >= 0.2) {
      return '#FF3B30'; // 红色 - 较差
    } else {
      return '#8E8E93'; // 灰色 - 很差
    }
  }
}

/**
 * 简化版测速仪表盘
 */
@Component
export struct SimpleSpeedometer {
  @Prop downloadSpeed: number = 0;
  @Prop uploadSpeed: number = 0;
  @Prop latency: number = 0;
  @Prop isRunning: boolean = false;
  
  build() {
    Column() {
      // 主要速度显示
      Row() {
        // 下载速度
        Column() {
          Text(this.formatSpeed(this.downloadSpeed))
            .fontSize(28)
            .fontWeight(FontWeight.Bold)
            .fontColor('#34C759')
          Text('下载')
            .fontSize(14)
            .fontColor('#666666')
            .margin({ top: 4 })
        }
        .layoutWeight(1)
        
        // 分隔线
        Divider()
          .vertical(true)
          .height(60)
          .color('#E5E5EA')
          .strokeWidth(1)
        
        // 上传速度
        Column() {
          Text(this.formatSpeed(this.uploadSpeed))
            .fontSize(28)
            .fontWeight(FontWeight.Bold)
            .fontColor('#FF9500')
          Text('上传')
            .fontSize(14)
            .fontColor('#666666')
            .margin({ top: 4 })
        }
        .layoutWeight(1)
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceEvenly)
      .padding(20)
      
      // 延迟显示
      if (this.latency > 0) {
        Row() {
          Text(`延迟: ${this.latency.toFixed(0)} ms`)
            .fontSize(16)
            .fontColor('#666666')
        }
        .margin({ top: 10 })
      }
      
      // 运行状态
      if (this.isRunning) {
        Row() {
          LoadingProgress()
            .width(20)
            .height(20)
            .color('#007AFF')
          Text('测试中...')
            .fontSize(14)
            .fontColor('#007AFF')
            .margin({ left: 8 })
        }
        .margin({ top: 15 })
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
  }
  
  /**
   * 格式化速度显示
   */
  private formatSpeed(speed: number): string {
    if (speed >= 1000) {
      return `${(speed / 1000).toFixed(2)} Gbps`;
    }
    return `${speed.toFixed(2)} Mbps`;
  }
}

/**
 * 网络质量评分组件
 */
@Component
export struct NetworkQualityScore {
  @Prop score: number = 0;
  @Prop description: string = '';
  
  build() {
    Column() {
      // 评分数值
      Text(`${this.score}`)
        .fontSize(64)
        .fontWeight(FontWeight.Bold)
        .fontColor(this.getScoreColor())
      
      // 评分描述
      Text(this.description)
        .fontSize(18)
        .fontColor('#666666')
        .margin({ top: 8 })
      
      // 标签
      Text('网络质量评分')
        .fontSize(12)
        .fontColor('#999999')
        .margin({ top: 4 })
    }
    .justifyContent(FlexAlign.Center)
    .padding(20)
  }
  
  /**
   * 获取评分对应的颜色
   */
  private getScoreColor(): string {
    if (this.score >= 90) {
      return '#34C759'; // 绿色 - 优秀
    } else if (this.score >= 75) {
      return '#007AFF'; // 蓝色 - 良好
    } else if (this.score >= 60) {
      return '#FF9500'; // 橙色 - 一般
    } else if (this.score >= 40) {
      return '#FF3B30'; // 红色 - 较差
    } else {
      return '#8E8E93'; // 灰色 - 很差
    }
  }
}
